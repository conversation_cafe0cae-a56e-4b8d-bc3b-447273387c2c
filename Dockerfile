# Stage 1: Build the application using Maven
FROM maven:3.9-eclipse-temurin-21 AS build

# Set the working directory
WORKDIR /app

# Copy the pom.xml and download dependencies first (for caching)
COPY pom.xml .
RUN mvn dependency:go-offline

# Copy the full source and build the application
COPY src ./src
RUN mvn clean package -DskipTests

# Stage 2: Run the application with a minimal JDK 21 runtime
FROM amazoncorretto:21.0.2-alpine3.19

# Set the working directory
WORKDIR /app

# Download the Datadog Java Agent
ADD https://dtdg.co/latest-java-tracer dd-java-agent.jar

# Copy the built JAR file from the builder stage
COPY --from=build /app/target/*.jar app.jar
ARG ACTIVE_PROFILE

# Expose the port the app runs on (change if needed)
EXPOSE 8080

# ENV DD_AGENT_HOST=datadog-apm.datadog-agent.svc.cluster.local
# ENV DD_TRACE_AGENT_URL=http://datadog-apm.datadog-agent.svc.cluster.local:8126
# ENV DD_ENV=qa
# ENV DD_SERVICE=qa-ivr-service
# ENV DD_VERSION=1.0
# ENV DD_TRACE_DEBUG=true
# ENV DD_LOGS_INJECTION=true
# ENV DD_TRACE_SAMPLE_RATE=1.0
# ENV DD_RUNTIME_METRICS_ENABLED=true
# ENV DD_PROFILING_ENABLED=true


# Run the application with Datadog Java Agent
ENTRYPOINT ["java", \
    "-javaagent:/app/dd-java-agent.jar", \
    "-Ddd.trace.enabled=true", \
    "-Ddd.logs.injection=true", \
    "-Dspring.profiles.active=qa", \
    "-jar", "app.jar"]