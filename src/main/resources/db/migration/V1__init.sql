-- Create disposition_master table
CREATE TABLE IF NOT EXISTS ticket_sub_category (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    category_id BIGINT NOT NULL,
    subtype VARCHAR(255) NOT NULL,
    qrc VARCHAR(255) NOT NULL,
    department VARCHAR(255) NOT NULL,
    priority VARCHAR(100) NOT NULL,
    sla JSONB,
    bucket VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create disposition_category_master table
CREATE TABLE IF NOT EXISTS ticket_category (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    category VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create partner_call_log table
CREATE TABLE partner_call_log (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    call_id VARCHAR(255) DEFAULT NULL,
    ivr_provider VARCHAR(255) DEFAULT NULL,
    from_number VARCHAR(255) DEFAULT NULL,
    to_number VARCHAR(255) DEFAULT NULL,
    call_status VARCHAR(50) DEFAULT NULL,
    caller_details JSONB,
    disposition_details JSONB,
    ticket_details JSONB,
    agent JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);