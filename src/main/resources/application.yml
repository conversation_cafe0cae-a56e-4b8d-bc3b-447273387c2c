spring.application.name: ivr

spring:
  datasource:
    url: jdbc:postgresql://${POSTGRES_DB_HOST}:${POSTGRES_DB_PORT}/${POSTGRES_DB_NAME}
    username: ${POSTGRES_DB_USERNAME}
    password: ${POSTGRES_DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    schemas: ${POSTGRES_DB_NAME}
    enabled: true
    locations: classpath:db/migration
    validateOnMigrate: true
    validateMigrationNaming: true

feign-config:
  config:
    booking:
      host: ${BOOKING_SERVICE_URL}
    payment:
      host: ${PAYMENT_SERVICE_URL}
    lead:
      host: ${LEAD_SERVICE_URL}
    remote:
      host: ${REMOTE_SERVICE_URL}

