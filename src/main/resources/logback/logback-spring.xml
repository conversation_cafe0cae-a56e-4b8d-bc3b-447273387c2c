<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="JSON_CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <timeZone>Etc/UTC</timeZone>
                    <pattern>yyyy-MM-dd'T'HH:mm:ss.SSSX</pattern>
                </timestamp>
                <callerData>
                    <classFieldName>class</classFieldName>
                    <methodFieldName>method</methodFieldName>
                    <fileFieldName>file</fileFieldName>
                    <lineFieldName>line</lineFieldName>
                </callerData>
                <loggerName>
                    <fieldName>logger</fieldName>
                </loggerName>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
                <logLevel>
                    <fieldName>level</fieldName>
                </logLevel>
                <message/>
                <mdc>
                    <fieldName>mdc</fieldName>
                </mdc>
                <mdc/>
                <stackTrace>
                    <fieldName>exception</fieldName>
                </stackTrace>
            </providers>
        </encoder>
    </appender>

    <appender name="LINE_CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} %-5p %c{1} %L [%M] - %m%n dd.trace_id: %X{dd.trace_id} dd.span_id:
                %X{dd.span_id}
            </Pattern>
        </layout>
    </appender>
    <springProfile name="!dev">
        <root level="INFO" additivity="false">
            <appender-ref ref="JSON_CONSOLE_APPENDER"/>
        </root>
    </springProfile>
    <springProfile name="dev">
        <root level="INFO" additivity="false">
            <appender-ref ref="LINE_CONSOLE_APPENDER"/>
        </root>
    </springProfile>
</configuration>