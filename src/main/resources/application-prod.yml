spring:
  config: classpath:logback/logback-spring.xml

POSTGRES_DB_HOST: X
POSTGRES_DB_PORT: X
POSTGRES_DB_NAME: X
POSTGRES_DB_USERNAME: X
POSTGRES_DB_PASSWORD: X

BOOKING_SERVICE_URL: https://booking-v2.i2e1.in
PAYMENT_SERVICE_URL: https://payment.i2e1.in
LEAD_SERVICE_URL: https://task.i2e1.in
REMOTE_SERVICE_URL: https://remote.i2e1.in
USER_REGISTRY_SERVICE_URL: https://user-registry.i2e1.in
KAPTURE_URL: X
KAPTURE_AUTH_TOKEN: X