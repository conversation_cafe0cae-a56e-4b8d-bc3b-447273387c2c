logging:
  config: classpath:logback/logback-spring.xml

POSTGRES_DB_HOST: qa-rds.cre2y0mswdtc.ap-south-2.rds.amazonaws.com
POSTGRES_DB_PORT: 5432
POSTGRES_DB_USERNAME: ivr_user
POSTGRES_DB_PASSWORD: 456yuieqfuk
POSTGRES_DB_NAME: ivr_db

#POSTGRES_DB_HOST: ivr-wiom-08d4.e.aivencloud.com
#POSTGRES_DB_PORT: 22699
#POSTGRES_DB_USERNAME: avnadmin
#POSTGRES_DB_PASSWORD: AVNS_neRmoV5UL7mLJ_oZi8X
#POSTGRES_DB_NAME: defaultdb



BOOKING_SERVICE_URL: https://booking-v2.qa.i2e1.in
PAYMENT_SERVICE_URL: https://payment.qa.i2e1.in
LEAD_SERVICE_URL: https://task.qa.i2e1.in
REMOTE_SERVICE_URL: https://remote.qa.i2e1.in
USER_REGISTRY_SERVICE_URL: https://user-registry.qa.i2e1.in
KAPTURE_URL: https://wiominstaging.kapturecrm.com
KAPTURE_AUTH_TOKEN: 'c2tjZXF4eTh3b251YzJoN292a3QwZW9yaDRucGtqZ3JoM2tqN2dudnhmajY0M3lrd3M='