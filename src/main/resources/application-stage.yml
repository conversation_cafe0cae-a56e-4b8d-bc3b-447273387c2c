spring:
  config: classpath:logback/logback-spring.xml

POSTGRES_DB_HOST: X
POSTGRES_DB_PORT: X
POSTGRES_DB_NAME: X
POSTGRES_DB_USERNAME: X
POSTGRES_DB_PASSWORD: X


BOOKING_SERVICE_URL: https://booking.stage.i2e1.in
PAYMENT_SERVICE_URL: https://payment.stage.i2e1.in
LEAD_SERVICE_URL: https://task.stage.i2e1.in
REMOTE_SERVICE_URL: https://remote.stage.i2e1.in
USER_REGISTRY_SERVICE_URL: https://user-registry.stage.i2e1.in
KAPTURE_URL: https://wiominstaging.kapturecrm.com
KAPTURE_AUTH_TOKEN: 'c2tjZXF4eTh3b251YzJoN292a3QwZW9yaDRucGtqZ3JoM2tqN2dudnhmajY0M3lrd3M='