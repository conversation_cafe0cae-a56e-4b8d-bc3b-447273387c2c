package com.wiom.ivr;

import com.wiom.ivr.config.RestClientConfiguration;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SpringBootApplication
@RequiredArgsConstructor
public class IvrApplication {

//	private final A a;

	public static void main(String[] args) {

		SpringApplication.run(IvrApplication.class, args);
	}

	@Configuration
	@RequiredArgsConstructor
	 class AppStartup implements ApplicationRunner {
		private final RestClientConfiguration.A rc;
		@Override
		public void run(ApplicationArguments args) {
			rc.f();
			System.out.println("ApplicationRunner started");
			// Your startup logic
		}
	}

//	@Bean
//	public A b() {
//		return new A();
//	}
//
//	public static class A {
//		public void f() {
//			System.out.println("Hello World!");
//		}
//	}
}
