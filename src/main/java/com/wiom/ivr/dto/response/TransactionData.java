package com.wiom.ivr.dto.response;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class TransactionData {
    private int planId;
    private int leadId;
    private String accountId;
    private String userId;
    private int settingId;
    private Boolean wiomMember;
    private Boolean subscription;
    private double discount;
    private String nasid;
    private String firstRechargeTime;
    private int time_limit;
    private Boolean doReset;
    private String mandateId;
    private String couponIds;
    private String membershipStartTime;
    private int quantity;
    private String billingAddress;
    private String deliveryAddress;
    private String webLink;
    private String invoice;
    private String gst;
    private String couponCode;
    private String planStartTime;
    private int revenueId;
    private String advancePay;
    private String connection;
    private String planName;
    private int billId;
    private String notificationStatus;
    private String paymentKey;
    private String uniqueIdentifier;
    private String totalRefundedAmount;
    private String mobile;
    private String transactionId;
    private BigDecimal payableAmount;
    private String source;
    private String createDate;
    private String updateDate;
    private Integer paymentStatus;
    private String txnStatus;
    private String responseMsg;
    private int paymentType;
    private String orderId;
    private String extraParam;
    private String entryDate;
    private String returnUrl;
    private String refundStatus;
    private String queueArn;
    private String queueKeyName;
    private String txnType;
    private String paymentMode;
    private ExtraData extraData;
    private String paidBy;
}