package com.wiom.ivr.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BookingData {
    private String mobile;
    private String addedTime;
    private String modifiedTime;
    
    @JsonProperty("task_id")
    private String taskId;
    
    private String name;
    private Integer status;
    private String address;
    private String googleAddress;
    private Long googleAddressId;
    private Object bookingPayment;
    private Object installationPayment;
    private Integer planId;
    private Integer settingId;
    private String ssid;
    private String wifiPass;
    private String bFeeCategory;
    private Boolean subscription;
    private Double lat;
    private Double lng;
    private Integer paymentStatus;
    private String leadType;
    private String otp;
    private String pref_inst_date;
    private Boolean hasPaidOnline;
    private Boolean wiomMember;
    private String bookingFeeRefundedDate;
    private String cancellationBookingDate;
    private String cancelReason;
    private String accountId;
    private String nasid;
    private String lcoAccountId;
    private String deviceId;
    private String integrationPoint;
    private String installationPaymentTxnId;
    private Integer installationReward;
    private Integer bookingReward;
    private Integer autopayReward;
    private String bookingPaymentTxnId;
    private Object eventMap;
    private Boolean isCouponApplied;
    private Object mandateInfo;
    private Double bookingRefundAmount;
    private Object rebookingPayment;
    private String rebookingPaymentTxnId;
    private Double bookingServiceCharge;
    private Integer bookingFee;
    private Boolean isInsDateChanged;
    private Boolean showDateChange;
    private Object offering;
    private Object autopayDiscountList;
    private String sharedCouponId;
    private String leadSquaredId;
    private String leadSquaredState;
    private Double sharedCouponDiscount;
    private Double bookedServiceCharge;
    private Long addedEpoch;
    private Boolean isAvailableInCity;
}