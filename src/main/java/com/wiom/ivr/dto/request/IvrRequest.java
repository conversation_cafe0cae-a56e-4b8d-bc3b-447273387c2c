package com.wiom.ivr.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class IvrRequest {

    @JsonProperty("uuid")
    private String id;

    @JsonProperty("call_id")
    private String callId;

    @JsonProperty("call_to_number")
    private String dialedNumber;

    @JsonProperty("caller_id_number")
    private String callerNumber;

    @JsonProperty("start_stamp")
    private String callStartTimestamp;

    @JsonProperty("last_dtmf")
    private String selectedOption;
}
