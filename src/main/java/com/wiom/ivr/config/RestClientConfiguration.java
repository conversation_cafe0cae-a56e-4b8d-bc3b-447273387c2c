package com.wiom.ivr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "feign-config")
public class RestClientConfiguration {

    private Map<String, RestClientProperties> config;

    @Data
    public static class RestClientProperties {
        private String host;
        private Map<String, String> headers = new HashMap<>();
    }

    @Bean
    public A c() {
        return new A();
    }

    public static class A {
        public void f() {
            System.out.println("from A: Hello World!");
        }
    }
}
