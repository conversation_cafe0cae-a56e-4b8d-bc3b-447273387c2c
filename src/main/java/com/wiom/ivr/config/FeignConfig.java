package com.wiom.ivr.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Decoder;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class FeignConfig {

    @Bean
    @Primary
    public Decoder responseExtractorDecoder(ObjectMapper objectMapper) {
        return new ResponseExtractorDecoder(
                new SpringDecoder(() -> new HttpMessageConverters()),
                objectMapper
        );
    }
}