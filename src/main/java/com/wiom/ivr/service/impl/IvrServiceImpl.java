package com.wiom.ivr.service.impl;

import com.wiom.ivr.dto.request.IvrRequest;
import com.wiom.ivr.dto.response.IVR;
import com.wiom.ivr.dto.response.IvrResponse;
import com.wiom.ivr.dto.response.Transfer;
import com.wiom.ivr.service.IvrService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IvrServiceImpl implements IvrService {

    @Override
    public IvrResponse processIncomingCall(IvrRequest ivrRequest) {
//        if (isCustomerRegistered(ivrRequest.getCallerNumber())) {
//            return new IvrResponse(List.of(new IVR(new Transfer("manual_ivr", "xxxx"))));
//        }

        return new IvrResponse(List.of(new IVR(new Transfer("agent", List.of("149057")))));
    }
}
