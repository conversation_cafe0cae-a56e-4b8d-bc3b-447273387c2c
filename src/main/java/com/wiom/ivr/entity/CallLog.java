package com.wiom.ivr.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "call_logs")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CallLog extends BaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uuid", nullable = false)
    private String uuid;

    @Column(name = "call_id", nullable = false)
    private String callId;

    @Column(name = "dialed_number")
    private String dialedNumber;

    @Column(name = "caller_number")
    private String callerNumber;

    @Column(name = "call_start_timestamp")
    private LocalDateTime callStartTimestamp;

}
