package com.wiom.ivr.client;

import com.wiom.ivr.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

public interface LeadServiceClient {

    @GetMapping("/api/v1/LeadAllocation/GetAllocationStatus")
    Map<String, String> getAllocationStatus(@RequestParam("mobile") String mobileNumber);
}
