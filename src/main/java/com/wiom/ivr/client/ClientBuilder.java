package com.wiom.ivr.client;

import com.wiom.ivr.config.RestClientConfiguration;
import feign.Contract;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.slf4j.Slf4jLogger;
import feign.okhttp.OkHttpClient;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Import({RestClientConfiguration.class, FeignClientsConfiguration.class})
public class ClientBuilder {

    private static final String BOOKING_CLIENT = "booking";
    private static final String PAYMENT_CLIENT = "payment";
    private static final String LEAD_CLIENT = "lead";
    private static final String REMOTE_CLIENT = "remote";

    @Bean
    public BookingServiceClient bookingServiceClient(
            Encoder encoder,
            Decoder decoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {
        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(BOOKING_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .client(new OkHttpClient())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(BookingServiceClient.class))
                .requestInterceptor(requestTemplate -> {
                    for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                        requestTemplate.header(entry.getKey(), entry.getValue());
                    }
                })
                .target(BookingServiceClient.class, properties.getHost());
    }

    @Bean
    public PaymentServiceClient paymentServiceClient(
            Encoder encoder,
            Decoder decoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {

        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(PAYMENT_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .client(new OkHttpClient())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(PaymentServiceClient.class))
                .requestInterceptor(requestTemplate -> {
                    for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                        requestTemplate.header(entry.getKey(), entry.getValue());
                    }
                })
                .target(PaymentServiceClient.class, properties.getHost());
    }

    @Bean
    public LeadServiceClient leadServiceClient(
            Encoder encoder,
            Decoder decoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {

        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(LEAD_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .client(new OkHttpClient())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(LeadServiceClient.class))
                .requestInterceptor(requestTemplate -> {
                    for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                        requestTemplate.header(entry.getKey(), entry.getValue());
                    }
                })
                .target(LeadServiceClient.class, properties.getHost());
    }

    @Bean
    public RemoteServiceClient remoteServiceClient(
            Encoder encoder,
            Decoder decoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {

        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(REMOTE_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .client(new OkHttpClient())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(RemoteServiceClient.class))
                .requestInterceptor(requestTemplate -> {
                    for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                        requestTemplate.header(entry.getKey(), entry.getValue());
                    }
                })
                .target(RemoteServiceClient.class, properties.getHost());
    }

}
