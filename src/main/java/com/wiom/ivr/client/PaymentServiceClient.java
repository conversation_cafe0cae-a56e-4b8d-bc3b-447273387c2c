package com.wiom.ivr.client;

import com.wiom.ivr.dto.response.BaseResponse;
import com.wiom.ivr.dto.response.TransactionData;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface PaymentServiceClient {
    
    @GetMapping("/api/OnlineTxnApi/GetTransactionList")
    List<TransactionData> getTransactions(@RequestParam("mobile") String mobileNumber);
}