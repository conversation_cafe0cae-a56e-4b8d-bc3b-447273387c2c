package com.wiom.ivr.controller;

import com.wiom.ivr.dto.request.IvrRequest;
import com.wiom.ivr.dto.response.IVR;
import com.wiom.ivr.service.IvrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/ivr")
public class IvrController {

    private final IvrService ivrService;

    @PostMapping
    public List<IVR> connectWithCustomer(@RequestBody IvrRequest ivrRequest) {
        log.info("connectWithCustomer {}", ivrRequest);
        return ivrService.processIncomingCall(ivrRequest).res();
    }
}
