package com.wiom.common.domain.dto.userRegistry;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.wiom.common.util.JsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PartnerDetails {

    private String name;
    private String email;
    private String pan;
    private LocalDateTime addedTime;
    private Long id;
    private Long rdAccountId;
    private String consumerNo;
    private String gst;
    private Double balance;
    private String address;
    private Long googleAddressId;
    private String aadhar;
    private Map<String, String> applications;
    private String extraData;
    private Map<String, Object> extraDataObject;
    private String logicalGroup;
    private PartnerOptions options;
    private Object version;

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PartnerOptions {

        @JsonProperty("usersJson")
        private String users;

        @JsonIgnore
        public List<User> getUsers() {
            return JsonUtil.jsonToList(users, User.class);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class User {
        @JsonProperty("user_id")
        private Long userId;

        private String username;
        private String name;

        @JsonProperty("contact_no")
        private String contactNo;

        private String email;

        @JsonProperty("mapping_params")
        private MappingParams mappingParams;

        @JsonProperty("added_time")
        private String addedTime;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MappingParams {
        private String role;

        @JsonCreator
        public static MappingParams from(Object value) {
            return JsonUtil.fromObject(value, MappingParams.class);
        }
    }
}
