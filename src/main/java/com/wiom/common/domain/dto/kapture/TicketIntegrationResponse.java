package com.wiom.common.domain.dto.kapture;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketIntegrationResponse {

    @JsonProperty("ticket")
    private Ticket ticket;

    @JsonProperty("ticket_id")
    private String ticketId;

    @JsonProperty("status")
    private String status;

    // todo this will be used once kapture has moved to new url. We be told by product.
    @JsonProperty("ticket_url")
    private String ticketUrl;

    @Setter
    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ticket {
        @JsonProperty("id")
        private String taskId;
    }

}