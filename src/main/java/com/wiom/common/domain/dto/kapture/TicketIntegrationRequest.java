package com.wiom.common.domain.dto.kapture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wiom.partner.applications.dto.request.TicketRequest;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import static com.wiom.partner.applications.constants.AppConstants.CALL_DIRECTION_INBOUND;
import static com.wiom.partner.applications.constants.AppConstants.CALL_STATUS_INSTANT;
import static com.wiom.partner.applications.constants.AppConstants.CALL_SUB_STATUS;
import static com.wiom.partner.applications.constants.AppConstants.TICKET_SOURCE_IVR;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketIntegrationRequest implements Serializable {
    private String title;
    private String ticketDetails;
    private String disposition;
    private String queue;
    private String dueDate;
    private String subStatus;
    private String customerCode;
    private String customerId;
    private String phone;
    private String priority;
    private String takeDispositionSla;
    private List<PartnerDetail> partnerDetails;
    private List<TicketSourceInfo> ticketSourceInfo;
    private List<PartnerDetailsAddInfo> partnerDetailsAddInfo;

    public static TicketIntegrationRequest of(TicketRequest ticketRequest, PartnerCallLogs partnerCallLogs,
                                              PartnerDetail partnerDetail, TicketSourceInfo ticketSourceInfo,
                                              PartnerDetailsAddInfo partnerDetailsAddInfo, LocalDateTime dueDate) {
        return TicketIntegrationRequest.builder()
                .title(ticketRequest.getSubtype())
                .ticketDetails(ticketRequest.getRemark())
                .disposition(String.format("%s|%s|%s",
                        ticketRequest.getCategory(),
                        ticketRequest.getBucket(),
                        ticketRequest.getSubtype()))
                .queue(ticketRequest.getDepartment())
                .dueDate(dueDate.toString())
                .subStatus(CALL_SUB_STATUS)
                .customerCode(partnerCallLogs.getCaller().getPartnerId().toString())
                .phone(partnerCallLogs.getFromNumber())
                .priority(String.valueOf(ticketRequest.getPriority().getValue()))
                .partnerDetails(List.of(partnerDetail))
                .ticketSourceInfo(List.of(ticketSourceInfo))
                .partnerDetailsAddInfo(List.of(partnerDetailsAddInfo))
                .build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartnerDetail implements Serializable{
        @JsonProperty("mapped_partner_name")
        private String mappedPartnerName;
        
        @JsonProperty("partner_mobile_number")
        private String partnerMobileNumber;
        
        private String zone;
        
        @JsonProperty("mapped_partner_(account_id)")
        private String mappedPartnerAccountId;
        
        private String city;

        public static PartnerDetail of(PartnerCallLogs partnerCallLogs) {
            return PartnerDetail.builder()
                    .city(partnerCallLogs.getCaller().getAddress())
                    .mappedPartnerAccountId(partnerCallLogs.getCaller().getPartnerId().toString())
                    .mappedPartnerName(partnerCallLogs.getCaller().getPartnerName())
                    .zone(partnerCallLogs.getCaller().getZone())
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TicketSourceInfo implements Serializable{
        @JsonProperty("ticket_source")
        private String ticketSource;
        
        private String direction;
        
        @JsonProperty("partner_app_ticket_title")
        private String partnerAppTicketTitle;
        
        @JsonProperty("call_status")
        private String callStatus;
        
        @JsonProperty("caller_role")
        private String callerRole;

        public static TicketSourceInfo of(PartnerCallLogs partnerCallLogs, TicketRequest ticketRequest) {
            return TicketSourceInfo.builder()
                    .ticketSource(TICKET_SOURCE_IVR)
                    .direction(CALL_DIRECTION_INBOUND)
                    .partnerAppTicketTitle(ticketRequest.getSubtype())
                    .callStatus(CALL_STATUS_INSTANT)
                    .callerRole(partnerCallLogs.getCaller().getRole())
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartnerDetailsAddInfo implements Serializable{
        @JsonProperty("customer_registered_mobile_number")
        private String customerRegisteredMobileNumber;
        
        private String qcr;

        @JsonProperty("customer_device_id")
        private String customerDeviceId;
        
        @JsonProperty("question_by_partner")
        private String questionByPartner;

        @JsonProperty("priority")
        private String priority;

        public static PartnerDetailsAddInfo of(TicketRequest ticketRequest) {
            return PartnerDetailsAddInfo.builder()
                    .customerRegisteredMobileNumber(ticketRequest.getCustomerMobileNumber())
                    .qcr(ticketRequest.getQrc())
                    .customerDeviceId(ticketRequest.getDeviceId())
                    .questionByPartner(ticketRequest.getQuestion())
                    .build();
        }
    }
}