package com.wiom.common.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@UtilityClass
public class DateUtil {

    public static LocalDate convertToDate(String dateString) {
        List<DateTimeFormatter> formatters = new ArrayList<>();
        // Iterate over the DateFormat enum values and add their patterns to the list of formatters
        for (DateFormat format : DateFormat.values()) {
            formatters.add(DateTimeFormatter.ofPattern(format.getPattern()));
        }

        // Attempt to parse the date string with each formatter
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(dateString, formatter);
            } catch (Exception ignored) {
                // Parsing failed with this formatter, try the next one
            }
        }
        // If parsing fails with all formatters, return null
        return null;
    }

    public static LocalTime convertToTime(String timeString) {
        List<DateTimeFormatter> formatters = new ArrayList<>();
        // Iterate over the TimeFormat enum values and add their patterns to the list of formatters
        for (TimeFormat format : TimeFormat.values()) {
            formatters.add(DateTimeFormatter.ofPattern(format.getPattern()));
        }

        // Attempt to parse the time string with each formatter
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalTime.parse(timeString, formatter);
            } catch (Exception ignored) {
                // Parsing failed with this formatter, try the next one
            }
        }
        // If parsing fails with all formatters, return null
        return null;
    }

    @Getter
    @RequiredArgsConstructor
    private enum DateFormat {
        DAY_MONTH_YEAR("dd-MM-yyyy"),
        YEAR_MONTH_DAY("yyyy-MM-dd"),
        MONTH_DAY_YEAR("MM/dd/yyyy"),
        DAY_MONTH_YEAR_TEXT("dd-MMM-yyyy"),
        YEAR_MONTH_DAY_TEXT("yyyy-MMM-dd"),
        MONTH_DAY_YEAR_TEXT("MMM/dd/yyyy");

        private final String pattern;
    }

    @Getter
    @RequiredArgsConstructor
    private enum TimeFormat {
        TIME_FORMAT_12_HOUR_AM_PM("hh:mm:ss a"),
        TIME_FORMAT_24_HOUR_NO_SECONDS("HH:mm"),
        TIME_FORMAT_24_HOUR_WITH_SECONDS("HH:mm:ss"),
        ISO_8601_FORMAT("HH:mm:ss.SSSXXX"),
        TIME_FORMAT_12_HOUR_NO_SECONDS("h:mm a");

        private final String pattern;
    }
}
