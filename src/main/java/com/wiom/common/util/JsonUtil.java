package com.wiom.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Collections;

@UtilityClass
public class JsonUtil {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    public String toJson(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    public <T> T fromJson(String json, Class<T> clazz) {
        try {
            return mapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * Converts a JSON string to a List of objects of the specified class
     *
     * @param <T>   Type of the objects in the list
     * @param json  JSON string to convert
     * @param clazz Class of the objects in the list
     * @return List of objects, or empty list if conversion fails or input is null/empty
     */
    public static <T> List<T> jsonToList(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            return mapper.readValue(json, 
                mapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            return Collections.emptyList();
        }
    }

    public static <T> T fromObject(Object value, Class<T> clazz) {
        if (value == null) {
            try {
                return clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                return null;
            }
        }

        try {
            if (value instanceof String) {
                return fromJson((String) value, clazz);
            } else {
                // Handle case where it's already a Map/object
                String json = toJson(value);
                return json != null ? fromJson(json, clazz) : null;
            }
        } catch (Exception e) {
            try {
                return clazz.getDeclaredConstructor().newInstance();
            } catch (Exception ex) {
                return null;
            }
        }
    }
}