package com.wiom.common.application.impl;

import com.wiom.common.domain.dto.kapture.TicketIntegrationRequest;
import com.wiom.common.domain.dto.kapture.TicketIntegrationResponse;
import com.wiom.common.infra.client.kapture.KaptureClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class KaptureService {

    private final KaptureClient kaptureClient;

    public TicketIntegrationResponse createTicket(List<TicketIntegrationRequest> request) {
        TicketIntegrationResponse response = null;
        try{
            response = kaptureClient.createTicket(request);
        }catch (Exception e){
            log.error("Error creating ticket to kapture.", e);
        }
        return response;
    }

}
