package com.wiom.common.application.impl;

import com.wiom.common.domain.dto.userRegistry.PartnerDetails;
import com.wiom.common.domain.dto.userRegistry.PartnerDetailsRequest;
import com.wiom.common.infra.client.userRegistry.AccountClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegistryIntegrationService {

    private final AccountClient accountClient;

    // todo add check on status
    public Optional<PartnerDetails> getPartnerDetails(String mobileNumber) {
        try {
            PartnerDetails partnerDetails = accountClient.getAccountFromMobile(PartnerDetailsRequest.builder().mobile(mobileNumber).build());
            return Optional.ofNullable(partnerDetails);
        }catch(Exception e) {
            log.warn("Error getting partner details for mobile number {}", mobileNumber, e);
            return Optional.empty();
        }
    }

}