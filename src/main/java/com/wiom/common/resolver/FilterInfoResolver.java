package com.wiom.common.resolver;

import com.wiom.common.filters.FilterInfo;
import com.wiom.common.filters.FilterQuery;
import com.wiom.common.util.DateUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FilterInfoResolver implements HandlerMethodArgumentResolver {

    private static final String STRING_FILTER_PARAM = "sf";
    private static final String NUMBER_FILTER_PARAM = "nf";
    private static final String DATE_FILTER_PARAM = "df";
    private static final String DATE_RANGE_FILTER_PARAM = "drf";
    private static final String TIME_FILTER_PARAM = "tf";
    public static final String MULTI_VALUE_FILTER_SPLIT = "-or-";
    private static final String FILTER_KEY_VALUE_SPLIT = ":";
    private static final String MULTI_VALUES_SPLIT = ",";
    public static final String SORT_ORDER_KEY = "orderDirection";
    public static final String SORT_BY_KEY = "orderBy";
    public static final String DEFAULT_SORT_KEY = "addedTime";
    public static final String DEFAULT_SORT_ORDER = "desc";

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType().equals(FilterInfo.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        FilterInfo filterData = new FilterInfo();
        filterData.setFilterQueryList(new ArrayList<>());
        HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();

        // Process different types of filters
        generateFilter(filterData, request.getParameterValues(STRING_FILTER_PARAM), FilterQuery.FilterType.STRING);
        generateFilter(filterData, request.getParameterValues(NUMBER_FILTER_PARAM), FilterQuery.FilterType.NUMERIC);
        generateFilter(filterData, request.getParameterValues(DATE_FILTER_PARAM), FilterQuery.FilterType.DATE);
        generateFilter(filterData, request.getParameterValues(TIME_FILTER_PARAM), FilterQuery.FilterType.TIME);

        // Process date range filters
        generateDateRangeFilter(filterData, request.getParameterValues(DATE_RANGE_FILTER_PARAM));

        // Set sorting parameters
        filterData.setOrderBy(ObjectUtils.isEmpty(request.getParameter(SORT_BY_KEY)) ? DEFAULT_SORT_KEY : request.getParameter(SORT_BY_KEY));
        filterData.setOrderDirection(ObjectUtils.isEmpty(request.getParameter(SORT_ORDER_KEY)) ? DEFAULT_SORT_ORDER : request.getParameter(SORT_ORDER_KEY));

        return filterData;
    }

    private void generateFilter(FilterInfo filterData, String[] parameterValues, FilterQuery.FilterType filterType) {
        if (parameterValues == null || parameterValues.length == 0) {
            return;
        }

        List<FilterQuery<?>> filters = new ArrayList<>();

        for (String filter : parameterValues) {
            String[] multiValueFilters = filter.split(MULTI_VALUE_FILTER_SPLIT);

            if (multiValueFilters.length > 1) {
                extractFilters(filterType, filters, multiValueFilters);
            } else {
                FilterQuery<?> newFilter = FilterQuery.createNewFilter(filterType);
                extractFilter(filterType, filters, filter, newFilter);
            }
        }

        filterData.getFilterQueryList().addAll(filters);
    }

    private void extractFilters(FilterQuery.FilterType filterType, List<FilterQuery<?>> filters, String[] multiValueFilters) {
        for (String filter : multiValueFilters) {
            FilterQuery<?> newFilter = FilterQuery.createNewFilter(filterType);
            extractFilter(filterType, filters, filter, newFilter);
        }
    }

    private void extractFilter(FilterQuery.FilterType filterType, List<FilterQuery<?>> filters, String filter,
                               FilterQuery<?> newFilter) {
        String[] attValuePair = filter.split(FILTER_KEY_VALUE_SPLIT, 2);
        if (attValuePair.length <= 1) {
            return; // No valid attribute-value pair found
        }

        String attribute = attValuePair[0];
        newFilter.setType(filterType);
        newFilter.setAttribute(attribute);
        newFilter.setValues(getFilterValues(filterType, attValuePair));
        newFilter.setOps(FilterQuery.Operations.EQ);
        filters.add(newFilter);
    }

    private List<?> getFilterValues(FilterQuery.FilterType filterType, String[] attValuePair) {
        return switch (filterType) {
            case STRING -> Arrays.stream(attValuePair[1].split(MULTI_VALUES_SPLIT)).collect(Collectors.toList());
            case NUMERIC -> Arrays.stream(attValuePair[1].split(MULTI_VALUES_SPLIT))
                    .map(this::castNumericValues)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            case DATE -> Arrays.stream(attValuePair[1].split(MULTI_VALUES_SPLIT))
                    .map(this::castToLocalDateTime)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            case TIME -> Arrays.stream(attValuePair[1].split(MULTI_VALUES_SPLIT))
                    .map(this::castToLocalTime)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        };
    }

    private LocalTime castToLocalTime(String s) {
        try {
            return DateUtil.convertToTime(s);
        } catch (Exception ex) {
            return null;
        }
    }

    private LocalDate castToLocalDate(String s) {
        try {
            return DateUtil.convertToDate(s);
        } catch (Exception ex) {
            return null;
        }
    }

    private LocalDateTime castToLocalDateTime(String s) {
        try {
            LocalDate date = DateUtil.convertToDate(s);
            if (date != null) {
                return date.atStartOfDay();
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    private Number castNumericValues(String s) {
        try {
            NumberFormat numberFormat = NumberFormat.getInstance();
            return numberFormat.parse(s);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * Generates date range filters from request parameters.
     * Format: attribute:fromDate,toDate
     * Example: addedTime:2023-01-01,2023-12-31
     */
    private void generateDateRangeFilter(FilterInfo filterData, String[] parameterValues) {
        if (parameterValues == null || parameterValues.length == 0) {
            return;
        }

        List<FilterQuery<?>> filters = new ArrayList<>();

        for (String filter : parameterValues) {
            String[] attValuePair = filter.split(FILTER_KEY_VALUE_SPLIT, 2);
            if (attValuePair.length <= 1) {
                continue;
            }

            String attribute = attValuePair[0];
            String valueStr = attValuePair[1];
            String[] dateValues = valueStr.split(MULTI_VALUES_SPLIT);

            if (dateValues.length < 2) {
                continue;
            }

            FilterQuery<?> newFilter = FilterQuery.createNewFilter(FilterQuery.FilterType.DATE);
            newFilter.setType(FilterQuery.FilterType.DATE);
            newFilter.setAttribute(attribute);

            LocalDate fromDate = castToLocalDate(dateValues[0]);
            LocalDate toDate = castToLocalDate(dateValues[1]);

            if (fromDate == null || toDate == null) {
                continue;
            }

            // Convert LocalDate to LocalDateTime (start of day for fromDate, end of day for toDate)
            List<LocalDateTime> dateTimeValues = new ArrayList<>();
            dateTimeValues.add(fromDate.atStartOfDay());
            dateTimeValues.add(toDate.atTime(23, 59, 59));

            newFilter.setValues(dateTimeValues);
            newFilter.setOps(FilterQuery.Operations.BETWEEN);
            filters.add(newFilter);
        }

        filterData.getFilterQueryList().addAll(filters);
    }
}