package com.wiom.common.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FilterQuery<T> {
    private FilterType type;
    private String attribute;
    private List<?> values;
    private Operations ops;

    public enum  FilterType {
        STRING, NUMERIC, DATE, TIME
    }

    public enum Operations {
        EQ,
        NOT_EQ,
        BETWEEN
    }

    public static FilterQuery<?> createNewFilter(FilterType filterType) {
        return switch (filterType) {
            case STRING -> new FilterQuery<String>();
            case NUMERIC -> new FilterQuery<Number>();
            case DATE -> new FilterQuery<LocalDate>();
            case TIME -> new FilterQuery<LocalTime>();
        };
    }
}