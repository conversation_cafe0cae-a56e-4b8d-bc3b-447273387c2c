package com.wiom.common.infra.client.userRegistry;

import com.wiom.common.domain.dto.userRegistry.PartnerDetails;
import com.wiom.common.domain.dto.userRegistry.PartnerDetailsRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface AccountClient {

    @PostMapping("/Customer/Accounts/GetAccountFromMobile")
    PartnerDetails getAccountFromMobile(@RequestBody PartnerDetailsRequest request);
}
