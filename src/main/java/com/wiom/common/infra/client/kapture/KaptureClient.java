package com.wiom.common.infra.client.kapture;

import com.wiom.common.domain.dto.kapture.TicketIntegrationRequest;
import com.wiom.common.domain.dto.kapture.TicketIntegrationResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface KaptureClient {

    @PostMapping(value = "/add-ticket-from-other-source.html/v.2.0")
    TicketIntegrationResponse createTicket(@RequestBody List<TicketIntegrationRequest> request);
}