package com.wiom.common.infra.client;

import com.wiom.common.infra.client.kapture.KaptureClient;
import com.wiom.common.infra.client.userRegistry.AccountClient;
import com.wiom.common.infra.config.RestClientConfiguration;
import com.wiom.common.infra.exception.CustomErrorDecoder;
import feign.Contract;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.jackson.JacksonDecoder;
import feign.slf4j.Slf4jLogger;
import feign.okhttp.OkHttpClient;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Import({RestClientConfiguration.class, FeignClientsConfiguration.class})
public class ClientBuilder {

    private static final String BOOKING_CLIENT = "booking";
    private static final String PAYMENT_CLIENT = "payment";
    private static final String LEAD_CLIENT = "lead";
    private static final String REMOTE_CLIENT = "remote";
    private static final String ACCOUNT_CLIENT = "account";
    private static final String KAPTURE_CLIENT = "kapture";


    @Bean
    public AccountClient accountClient(
            Encoder encoder,
            Decoder decoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {
        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(ACCOUNT_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(decoder)
                .contract(contract)
                .client(new OkHttpClient())
                .errorDecoder(new CustomErrorDecoder())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(AccountClient.class))
                .requestInterceptor(requestTemplate -> {
                    for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                        requestTemplate.header(entry.getKey(), entry.getValue());
                    }
                })
                .target(AccountClient.class, properties.getHost());
    }


    // Add this method to ClientBuilder
    @Bean
    public KaptureClient kaptureClient(
            Encoder encoder,
            Contract contract,
            RestClientConfiguration restClientConfiguration) {

        RestClientConfiguration.RestClientProperties properties =
                restClientConfiguration.getConfig().get(KAPTURE_CLIENT);

        return Feign.builder()
                .encoder(encoder)
                .decoder(new JacksonDecoder())
                .contract(contract)
                .client(new OkHttpClient())
                .logLevel(Logger.Level.FULL)
                .retryer(Retryer.NEVER_RETRY)
                .logger(new Slf4jLogger(KaptureClient.class))
                .requestInterceptor(requestTemplate -> {
                    // Only add headers that are not already set
                    if (requestTemplate.headers().get("Authorization") == null) {
                        for (Map.Entry<String, String> entry : properties.getHeaders().entrySet()) {
                            requestTemplate.header(entry.getKey(), entry.getValue());
                        }
                    }
                })
                .target(KaptureClient.class, properties.getHost());
    }

}
