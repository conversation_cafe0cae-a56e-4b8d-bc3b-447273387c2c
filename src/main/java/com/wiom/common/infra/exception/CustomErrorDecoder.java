package com.wiom.common.infra.exception;

import feign.Response;
import feign.codec.ErrorDecoder;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
public class CustomErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        switch (response.status()) {
            case 400:
                return new ServiceException(HttpStatus.BAD_REQUEST, "Bad request");
            case 404:
                return new ServiceException(HttpStatus.NOT_FOUND, "Not found");
            case 500:
                return new ServiceException(HttpStatus.INTERNAL_SERVER_ERROR, "Server error");
            default:
                return defaultDecoder.decode(methodKey, response);
        }
    }
}