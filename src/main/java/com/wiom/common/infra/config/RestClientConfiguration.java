package com.wiom.common.infra.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "feign-config")
public class RestClientConfiguration {

    private Map<String, RestClientProperties> config;

    @Data
    public static class RestClientProperties {
        private String host;
        private Map<String, String> headers = new HashMap<>();
    }
}
