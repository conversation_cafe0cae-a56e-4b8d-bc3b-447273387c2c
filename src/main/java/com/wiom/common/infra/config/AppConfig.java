package com.wiom.common.infra.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.List;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "app-config")
@PropertySource(value = "classpath:app-config.yml", factory = YamlPropertySourceFactory.class)
public class AppConfig {

    private String ticketBaseUrl;
    private Telecom telecom;

    @Getter
    @Setter
    public static class Telecom {
        private ServiceRedirection found;
        private ServiceRedirection notFound;
    }

    @Getter
    @Setter
    public static class ServiceRedirection {
        private String type;
        private List<String> code;
    }

}