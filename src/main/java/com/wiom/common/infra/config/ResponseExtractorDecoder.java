package com.wiom.common.infra.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wiom.common.domain.dto.BaseResponse;
import feign.Response;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

@Slf4j
public class ResponseExtractorDecoder implements Decoder {

    private final Decoder delegate;
    private final ObjectMapper objectMapper;

    public ResponseExtractorDecoder(Decoder delegate, ObjectMapper objectMapper) {
        this.delegate = delegate;
        this.objectMapper = objectMapper;
    }

    @Override
    public Object decode(Response response, Type type) throws IOException {
        try (InputStream bodyIs = response.body().asInputStream()) {
            BaseResponse<?> baseResponse = objectMapper.readValue(bodyIs, BaseResponse.class);
            // Serialize just the "data" field into expected type
            byte[] dataBytes = objectMapper.writeValueAsBytes(baseResponse.getData());
            return objectMapper.readValue(dataBytes, objectMapper.constructType(type));
        }
    }
}