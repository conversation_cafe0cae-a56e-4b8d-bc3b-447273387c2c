package com.wiom.partner.util;

import lombok.experimental.UtilityClass;

import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static java.util.concurrent.TimeUnit.DAYS;
import static java.util.concurrent.TimeUnit.HOURS;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.concurrent.TimeUnit.SECONDS;

@UtilityClass
public class CommonUtil {

    /**
     * Converts a duration value to minutes based on the provided TimeUnit.
     * @param timeUnit The time unit of the duration (e.g., HOURS, DAYS)
     * @param durationValue The duration value to convert
     * @return The duration in minutes
     * @throws IllegalArgumentException if timeUnit is null or not supported
     */
    public static long convertToMinutes(TimeUnit timeUnit, long durationValue) {
        if (timeUnit == null) {
            throw new IllegalArgumentException("TimeUnit cannot be null");
        }

        return switch (timeUnit) {
            case SECONDS -> TimeUnit.SECONDS.toMinutes(durationValue);
            case MINUTES -> durationValue;
            case HOURS -> TimeUnit.HOURS.toMinutes(durationValue);
            case DAYS -> TimeUnit.DAYS.toMinutes(durationValue);
            default -> durationValue;
        };
    }

}
