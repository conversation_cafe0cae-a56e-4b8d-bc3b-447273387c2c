package com.wiom.partner.applications.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CallStatus {

    INITIATED("Initiated"),
    CONNECTED("Connected"),
    COMPLETED("Completed"),
    DISCONNECTED("Disconnected"),
    FOLLOW_UP("Follow Up"),
    UNREGISTERED("Unregistered"),
    MISSED("Missed");


    private final String value;
    private static final Map<String, CallStatus> BY_VALUE = new HashMap<>();

    public static List<String> dispositionStatuses() {
        return List.of(DISCONNECTED.value, FOLLOW_UP.value, COMPLETED.value);
    }

    static {
        for (CallStatus status : values()) {
            BY_VALUE.put(status.value.toLowerCase(), status);
        }
    }

    public static CallStatus fromValue(String value) {
        return BY_VALUE.get(value);
    }

}
