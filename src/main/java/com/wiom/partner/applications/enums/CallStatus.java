package com.wiom.partner.applications.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CallStatus {

    INITIATED("Initiated", ""),
    CONNECTED("Connected", "answered"),
    COMPLETED("Completed", ""),
    DISCONNECTED("Disconnected", ""),
    FOLLOW_UP("Follow Up", ""),
    UNREGISTERED("Unregistered", ""),
    MISSED("Missed", "missed");


    private final String value;
    private final String tatateleValue;
    private static final Map<String, CallStatus> BY_VALUE = new HashMap<>();
    private static final Map<String, CallStatus> BY_TATATELE_VALUE = new HashMap<>();

    public static List<String> dispositionStatuses() {
        return List.of(DISCONNECTED.value, FOLLOW_UP.value, COMPLETED.value);
    }

    static {
        for (CallStatus status : values()) {
            BY_VALUE.put(status.value.toLowerCase(), status);
        }
    }

    static{
        BY_TATATELE_VALUE.put(CONNECTED.tatateleValue, CONNECTED);
        BY_TATATELE_VALUE.put(MISSED.tatateleValue, MISSED);
    }

    public static CallStatus fromValue(String value) {
        return BY_VALUE.get(value);
    }
    public static CallStatus fromTataTeleValue(String value) { return BY_TATATELE_VALUE.get(value); }

}
