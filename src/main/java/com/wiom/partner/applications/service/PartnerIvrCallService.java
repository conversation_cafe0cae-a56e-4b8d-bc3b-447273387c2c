package com.wiom.partner.applications.service;


import com.wiom.partner.applications.dto.tataTele.TataTelecomPartnerRequest;
import com.wiom.partner.applications.dto.response.PaginatedResponse;
import com.wiom.partner.applications.dto.tataTele.TataTelecom;
import com.wiom.partner.applications.dto.response.PartnerDetailsResponse;
import com.wiom.partner.applications.dto.webhook.CallStatusUpdateRequest;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import com.wiom.common.filters.FilterInfo;

import java.util.List;

public interface PartnerIvrCallService {

    List<TataTelecom> handleIncomingCall(TataTelecomPartnerRequest tataTelecomPartnerRequest);

    PartnerDetailsResponse initializePartnerCallSession(String callId);

    void saveDisposition(String callId, PartnerCallLogs.Disposition disposition);

    PaginatedResponse<PartnerCallLogs> fetchCallLogs(int pageNumber, int pageSize, String fromNumber, String partnerName,
                                                     CallStatus status, FilterInfo query);

    /**
     * Updates the call status and agent for a given call ID
     * @param request
     */
    void updateCallStatusAndAgent(CallStatusUpdateRequest request);
}
