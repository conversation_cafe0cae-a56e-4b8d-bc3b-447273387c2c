package com.wiom.partner.applications.service.impl;

import com.wiom.common.infra.exception.ServiceException;
import com.wiom.partner.applications.service.TicketMetadataService;
import com.wiom.partner.applications.dto.callLogs.SLAConfiguration;
import com.wiom.partner.applications.dto.request.TicketCategoryRequest;
import com.wiom.partner.applications.dto.TicketMetadata;
import com.wiom.partner.applications.dto.response.TicketReferenceData;
import com.wiom.partner.domain.entity.TicketCategory;
import com.wiom.partner.domain.entity.TicketSubCategory;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.domain.repository.TicketCategoryRepository;
import com.wiom.partner.domain.repository.TicketSubCategoryRepository;
import com.wiom.partner.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TicketMetadataServiceImpl implements TicketMetadataService {

    private final TicketSubCategoryRepository ticketSubCategoryRepository;
    private final TicketCategoryRepository ticketCategoryRepository;


    @Override
    public TicketReferenceData getTicketMetadata() {
        log.debug("Fetching all disposition master data with categories");
        // Fetch all disposition masters
        List<TicketSubCategory> tickets = ticketSubCategoryRepository.findAllByOrderById();
        Map<Long, String> categoriesByMasterId = ticketCategoryRepository.findAllByOrderById().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(TicketCategory::getId, TicketCategory::getCategory));
        return TicketReferenceData.builder()
                .ticketSubCategories(fetchTicketSubCategoryMeta(tickets, categoriesByMasterId))
                .dispositionStatus(CallStatus.dispositionStatuses())
                .build();
    }

    private List<TicketMetadata> fetchTicketSubCategoryMeta(List<TicketSubCategory> tickets,
                                          Map<Long, String> categoriesByMasterId) {
        return tickets.stream()
                .map(master -> TicketMetadata.builder()
                        .id(master.getId())
                        .subtype(master.getSubtype())
                        .qrc(master.getQrc())
                        .department(master.getDepartment())
                        .priority(master.getPriority())
                        .sla(master.getSla())
                        .bucket(master.getBucket())
                        .category(categoriesByMasterId.get(master.getCategoryId()))
                        .build())
                .collect(Collectors.toList());
    }

    @Transactional
    public void saveTicketMetadata(List<TicketMetadata> masterDataList) {
        for (TicketMetadata request : masterDataList) {
            // Handle DispositionMaster - create new or update existing
            TicketCategory category = ticketCategoryRepository.findByCategory(request.getCategory()).orElse(new TicketCategory());
            category.setCategory(request.getCategory());
            ticketCategoryRepository.save(category);
            TicketSubCategory subCategory = request.getId() != null ? ticketSubCategoryRepository.findBySubtype(request.getSubtype())
                            .orElse(new TicketSubCategory()) : new TicketSubCategory();
            // Update master fields
            updateTicketSubCatgoryMetadata(request, subCategory, category);
            // Save DispositionMaster (creates new or updates existing)
            ticketSubCategoryRepository.save(subCategory);
        }
    }

    private void updateTicketSubCatgoryMetadata(TicketMetadata request, TicketSubCategory subCategory, TicketCategory category) {
        SLAConfiguration sla = request.getSla();
        if (sla != null) sla.setDurationInMinutes(CommonUtil.convertToMinutes( sla.getTimeUnit(), sla.getDurationValue()));
        subCategory.setSubtype(request.getSubtype());
        subCategory.setQrc(request.getQrc());
        subCategory.setDepartment(request.getDepartment());
        subCategory.setPriority(request.getPriority());
        subCategory.setSla(sla);
        subCategory.setBucket(request.getBucket());
        subCategory.setCategoryId(category.getId());
    }

    @Override
    public void updateTicketCategory(TicketCategoryRequest masterDataList) {
        TicketCategory category = ticketCategoryRepository.findByCategory(masterDataList.getOldCategory())
                .orElseThrow(() -> new ServiceException(HttpStatus.BAD_REQUEST, "No category found for the given category: " + masterDataList.getOldCategory()));
        category.setCategory(masterDataList.getNewCategory());
        ticketCategoryRepository.save(category);
    }

}
