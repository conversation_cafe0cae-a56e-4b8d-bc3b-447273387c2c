package com.wiom.partner.applications.service.impl;

import com.wiom.partner.applications.dto.webhook.CallStatusUpdateRequest;
import com.wiom.partner.applications.service.PartnerCallWebhookService;
import com.wiom.partner.applications.service.PartnerIvrCallService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PartnerCallWebhookServiceImpl implements PartnerCallWebhookService {

    private final PartnerIvrCallService partnerIvrCallService;

    @Override
    public void updateCallStatus(CallStatusUpdateRequest request) {
        log.info("Updating call status for callId: {}, status: {}, agent: {}",
                request.getCallId(), request.getCallStatus(), request.getAgentName());
        // Update call status using the existing service
        partnerIvrCallService.updateCallStatusAndAgent(request);
    }
}
