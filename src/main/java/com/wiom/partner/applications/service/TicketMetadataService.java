package com.wiom.partner.applications.service;

import com.wiom.partner.applications.dto.request.TicketCategoryRequest;
import com.wiom.partner.applications.dto.TicketMetadata;
import com.wiom.partner.applications.dto.response.TicketReferenceData;

import java.util.List;

public interface TicketMetadataService {

    /**
     * Retrieves all ticket master data records
     * @return List of all ticket master data records
     */
    TicketReferenceData getTicketMetadata();
    
    /**
     * Saves or updates a list of ticket master data records
     * @param masterDataList List of ticket master data to save/update
     * @return List of saved/updated ticket master data records
     */
    void saveTicketMetadata(List<TicketMetadata> masterDataList);

    /**
     * Updates a ticket category
     * @param masterDataList Ticket category to update
     */
    void updateTicketCategory(TicketCategoryRequest masterDataList);

}
