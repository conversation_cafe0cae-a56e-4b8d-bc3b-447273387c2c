package com.wiom.partner.applications.service.impl;

import com.wiom.common.application.impl.UserRegistryIntegrationService;
import com.wiom.common.domain.dto.userRegistry.PartnerDetails;
import com.wiom.common.filters.FilterInfo;
import com.wiom.common.infra.config.AppConfig;
import com.wiom.common.infra.exception.ServiceException;
import com.wiom.partner.applications.constants.AppConstants;
import com.wiom.partner.applications.dto.response.PaginatedResponse;
import com.wiom.partner.applications.dto.response.PartnerDetailsResponse;
import com.wiom.partner.applications.dto.tataTele.TataTelecom;
import com.wiom.partner.applications.dto.tataTele.TataTelecomPartnerRequest;
import com.wiom.partner.applications.dto.tataTele.Transfer;
import com.wiom.partner.applications.dto.webhook.CallStatusUpdateRequest;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.applications.enums.IvrProvider;
import com.wiom.partner.applications.service.PartnerIvrCallService;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import com.wiom.partner.domain.repository.PartnerCallLogsRepository;
import com.wiom.partner.domain.specification.PartnerCallDetailsSpecification;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PartnerIvrCallServiceImpl implements PartnerIvrCallService {

    private final AppConfig appConfig;
    private final PartnerCallLogsRepository partnerCallLogsRepository;
    private final UserRegistryIntegrationService userRegistryIntegrationService;

    @Override
    public List<TataTelecom> handleIncomingCall(TataTelecomPartnerRequest tataTelecomPartnerRequest) {
        // New entry to be created for new calls and for existing call will be updated
        PartnerCallLogs partnerCallLogs = partnerCallLogsRepository.findByCallId(tataTelecomPartnerRequest.getCallId())
                .orElse(new PartnerCallLogs());
        String callerId = tataTelecomPartnerRequest.getCallerIdNumber().length() > 10 ?
                tataTelecomPartnerRequest.getCallerIdNumber().substring(tataTelecomPartnerRequest.getCallerIdNumber().length() - 10) :
                tataTelecomPartnerRequest.getCallerIdNumber();
        partnerCallLogs.setFromNumber(callerId.length() > 10 ? callerId.substring(callerId.length() - 10) : callerId);
        partnerCallLogs.setCallId(tataTelecomPartnerRequest.getCallId());
        partnerCallLogs.setToNumber(tataTelecomPartnerRequest.getCallToNumber());
        partnerCallLogs.setIvrProvider(IvrProvider.TATA_TELE_SERVICE);
        updatePartnerMetadata(callerId, partnerCallLogs);
        partnerCallLogsRepository.save(partnerCallLogs);
        return createPartnerCallDetails(partnerCallLogs);
    }

    private List<TataTelecom> createPartnerCallDetails(PartnerCallLogs partnerCallLogs) {
        if (CallStatus.UNREGISTERED.equals(partnerCallLogs.getCallStatus())) {
            return List.of(new TataTelecom(new
                    Transfer(appConfig.getTelecom().getNotFound().getType(), appConfig.getTelecom().getNotFound().getCode())));
        }
        return List.of(new TataTelecom(new
                Transfer(appConfig.getTelecom().getFound().getType(), appConfig.getTelecom().getFound().getCode())));
    }

    private void updatePartnerMetadata(String fromNumber, PartnerCallLogs partnerCallLogs) {
        Optional<PartnerDetails> partnerDetails = userRegistryIntegrationService.getPartnerDetails(fromNumber);
        if (partnerDetails.isEmpty()) {
            partnerCallLogs.setCallStatus(CallStatus.UNREGISTERED);
            return;
        }
        PartnerCallLogs.Caller caller = getCaller(fromNumber, partnerCallLogs, partnerDetails.get());
        partnerCallLogs.setCallStatus(CallStatus.INITIATED);
        partnerCallLogs.setCaller(caller);
    }

    private PartnerCallLogs.Caller getCaller(String fromNumber, PartnerCallLogs partnerCallLogs, PartnerDetails partnerDetails) {
        PartnerCallLogs.Caller caller = Objects.isNull(partnerCallLogs.getCaller()) ? new PartnerCallLogs.Caller() :
                partnerCallLogs.getCaller();
        caller.setPartnerName(partnerDetails.getName());
        caller.setPartnerId(partnerDetails.getId());
        caller.setZone(partnerDetails.getLogicalGroup());
        caller.setAddress(partnerDetails.getAddress());
        List<PartnerDetails.User> users = partnerDetails.getOptions().getUsers();
        if (!CollectionUtils.isEmpty(users)) {
            PartnerDetails.User user = users.stream()
                    .filter(u -> u.getContactNo().equals(fromNumber))
                    .findFirst()
                    .orElse(null);
            if (user != null) {
                caller.setRole(Objects.nonNull(user.getMappingParams()) ? user.getMappingParams().getRole() :
                        AppConstants.ROHIT);
                caller.setName(user.getName());
            }
        }
        return caller;
    }

    @Override
    public PartnerDetailsResponse initializePartnerCallSession(String callId) {
        // Fetch partner details based on fromNumber
        PartnerDetailsResponse response = new PartnerDetailsResponse();
        PartnerCallLogs partnerCallLogs = partnerCallLogsRepository.findByCallId(callId)
                .orElseThrow(() -> new ServiceException(HttpStatus.BAD_REQUEST, "No partner found for the given call id: " + callId));
        if (!CallStatus.CONNECTED.equals(partnerCallLogs.getCallStatus())) {
            partnerCallLogs.setCallStatus(CallStatus.CONNECTED);
            partnerCallLogsRepository.save(partnerCallLogs);
        }
        if (Objects.nonNull(partnerCallLogs.getCaller())) {
            response = PartnerDetailsResponse.builder()
                    .partnerName(partnerCallLogs.getCaller().getPartnerName())
                    .tag(partnerCallLogs.getCaller().getZone())
                    .callerRole(partnerCallLogs.getCaller().getRole())
                    .callerName(partnerCallLogs.getCaller().getName())
                    .disposition(partnerCallLogs.getDisposition())
                    .build();
            fetchTicket(partnerCallLogs, response);
        }
        return response;
    }

    private void fetchTicket(PartnerCallLogs partnerCallLogs, PartnerDetailsResponse response) {
        if(Objects.isNull(partnerCallLogs.getTicket())) return;
        PartnerCallLogs.Ticket ticket = new PartnerCallLogs.Ticket();
        ticket.setId(partnerCallLogs.getTicket().getId());
        ticket.setStatus(partnerCallLogs.getTicket().getStatus());
        ticket.setUrl(partnerCallLogs.getTicket().getUrl());
        ticket.setRequest(partnerCallLogs.getTicket().getRequest());
        response.setTicket(ticket);
    }

    @Override
    public void saveDisposition(String callId, PartnerCallLogs.Disposition disposition) {
        PartnerCallLogs partnerCallLogs = partnerCallLogsRepository.findByCallId(callId)
                .orElseThrow(() -> new ServiceException(HttpStatus.BAD_REQUEST, "No partner found for the given call id: " + callId));
        partnerCallLogs.setDisposition(disposition);
        CallStatus callStatus = CallStatus.fromValue(disposition.getStatus());
        partnerCallLogs.setCallStatus(callStatus);
        partnerCallLogsRepository.save(partnerCallLogs);
        log.debug("Saved disposition for callId: {}", callId);
    }

    @Override
    public void updateCallStatusAndAgent(CallStatusUpdateRequest request) {
        log.info("Updating call status for :{}", request);
        PartnerCallLogs partnerCallLogs = partnerCallLogsRepository.findByCallId(request.getCallId()).orElse(new PartnerCallLogs());
        partnerCallLogs.setCallId(request.getCallId());
        // Update agent details if provided
        PartnerCallLogs.Agent agent = PartnerCallLogs.Agent.of(request);
        partnerCallLogs.setAgent(agent);
        // Update call status if provided
        if (StringUtils.isNotBlank(request.getCallStatus())){
            CallStatus callStatus = CallStatus.fromTataTeleValue(request.getCallStatus());
            if (StringUtils.isNotBlank(request.getHangupCause()) && CallStatus.CONNECTED.equals(callStatus)) {
                partnerCallLogs.setCallStatus(CallStatus.COMPLETED);
            }
            else if(callStatus!=null) {
                partnerCallLogs.setCallStatus(callStatus);
            }
        }
        // Save the updated/created log
        partnerCallLogsRepository.save(partnerCallLogs);
        log.info("Successfully updated call log for callId: {}", request.getCallId());
    }

    @Override
    public PaginatedResponse<PartnerCallLogs> fetchCallLogs(int pageNumber, int pageSize, String fromNumber, String partnerName,
                                                            CallStatus status, FilterInfo query) {
        try {
            // Create pageable for pagination (0-based page number)
            Pageable pageable = PageRequest.of(
                    pageNumber - 1, // Convert to 0-based index
                    pageSize,
                    Sort.by(Sort.Direction.DESC, AppConstants.CREATED_AT) // Sort by creation date, newest first
            );

            // Build specification using PartnerCallDetailsSpecification
            Specification<PartnerCallLogs> specification = PartnerCallDetailsSpecification.build(
                    query, fromNumber, partnerName, status);

            // Execute the query with the specification
            Page<PartnerCallLogs> page = partnerCallLogsRepository.findAll(specification, pageable);

            // Log the results for debugging
            log.debug("Found {} call details out of {} total",
                    page.getNumberOfElements(), page.getTotalElements());

            // Build and return the paginated response
            return PaginatedResponse.<PartnerCallLogs>builder()
                    .count(page.getTotalElements())
                    .pageNumber(page.getNumber() + 1) // Convert back to 1-based index
                    .pageSize(page.getSize())
                    .totalPages(page.getTotalPages())
                    .data(page.getContent())
                    .build();
        } catch (Exception e) {
            log.error("Error fetching call details: {}", e.getMessage(), e);
            return PaginatedResponse.<PartnerCallLogs>builder()
                    .count(0L)
                    .pageNumber(pageNumber)
                    .pageSize(pageSize)
                    .totalPages(0)
                    .data(Collections.emptyList())
                    .build();
        }
    }
}