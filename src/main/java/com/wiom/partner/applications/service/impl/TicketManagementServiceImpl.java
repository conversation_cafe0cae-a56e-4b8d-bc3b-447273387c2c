package com.wiom.partner.applications.service.impl;

import com.wiom.common.application.impl.KaptureService;
import com.wiom.common.domain.dto.kapture.TicketIntegrationRequest;
import com.wiom.common.domain.dto.kapture.TicketIntegrationResponse;
import com.wiom.common.infra.config.AppConfig;
import com.wiom.common.infra.exception.ServiceException;
import com.wiom.partner.applications.dto.callLogs.SLAConfiguration;
import com.wiom.partner.applications.dto.callLogs.Ticket;
import com.wiom.partner.applications.dto.request.TicketRequest;
import com.wiom.partner.applications.dto.response.TicketResponse;
import com.wiom.partner.applications.enums.TicketStatus;
import com.wiom.partner.applications.service.TicketManagementService;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import com.wiom.partner.domain.repository.PartnerCallLogsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class TicketManagementServiceImpl implements TicketManagementService {

    private final AppConfig appConfig;
    private final KaptureService kaptureService;
    private final PartnerCallLogsRepository partnerCallLogsRepository;

    @Override
    public TicketResponse raiseTicket(String callId, TicketRequest ticketRequest) {
        // Validate the request
        validate(ticketRequest);
        
        // Find the partner call log
        PartnerCallLogs partnerCallLogs = partnerCallLogsRepository.findByCallId(callId)
                .orElseThrow(() -> new ServiceException(HttpStatus.BAD_REQUEST, "No partner found for the given call id: " + callId));
                
        // Return existing ticket if it's already created
        if(Objects.nonNull(partnerCallLogs.getTicket()) && TicketStatus.CREATED.equals(partnerCallLogs.getTicket().getStatus())) {
            return TicketResponse.of(partnerCallLogs.getTicket());
        }
        // Create the main TicketIntegrationRequest
        TicketIntegrationRequest integrationRequest = generateTicketIntegrationRequest(ticketRequest, partnerCallLogs);
        // Call Kapture service to create ticket
        TicketIntegrationResponse kaptureResponse = kaptureService.createTicket(List.of(integrationRequest));
        // Create Ticket object
        Ticket ticket = Ticket.of(ticketRequest, kaptureResponse, integrationRequest, appConfig.getTicketBaseUrl());
        // Update partner call logs with ticket information
        partnerCallLogs.setTicket(ticket);
        partnerCallLogsRepository.save(partnerCallLogs);
        return TicketResponse.of(ticket);
    }

    private TicketIntegrationRequest generateTicketIntegrationRequest(TicketRequest ticketRequest, PartnerCallLogs partnerCallLogs) {
        // Create TicketIntegrationRequest
        LocalDateTime dueDate = LocalDateTime.now().plusMinutes(ticketRequest.getSla().getDurationInMinutes());
        // Create TicketIntegrationRequest.PartnerDetail
        TicketIntegrationRequest.PartnerDetail partnerDetail = TicketIntegrationRequest.PartnerDetail.of(partnerCallLogs);
        // Create TicketIntegrationRequest.TicketSourceInfo
        TicketIntegrationRequest.TicketSourceInfo ticketSourceInfo = TicketIntegrationRequest.TicketSourceInfo.of(partnerCallLogs, ticketRequest);
        // Create TicketIntegrationRequest.PartnerDetailsAddInfo
        TicketIntegrationRequest.PartnerDetailsAddInfo partnerDetailsAddInfo = TicketIntegrationRequest.PartnerDetailsAddInfo.of(ticketRequest);
        return TicketIntegrationRequest.of(ticketRequest, partnerCallLogs, partnerDetail, ticketSourceInfo, partnerDetailsAddInfo, dueDate);
    }

    private void validate(TicketRequest ticketRequest) {
        if (Objects.isNull(ticketRequest)) {
            throw new ServiceException(HttpStatus.BAD_REQUEST, "Invalid ticket request.");
        }
        // Validate required fields
        validateField(ticketRequest.getCategory(), "category");
        validateField(ticketRequest.getSubtype(), "subtype");
        validateField(ticketRequest.getBucket(), "bucket");
        validateField(ticketRequest.getDepartment(), "department");
        validateField(ticketRequest.getPriority(), "priority");
        validateField(ticketRequest.getSla(), "sla");
        validateField(ticketRequest.getRemark(), "remark");
        validateField(ticketRequest.getCustomerMobileNumber(), "customerMobileNumber");
        // Validate SLA configuration
        SLAConfiguration sla = ticketRequest.getSla();
        if (sla.getTimeUnit() == null) {
            throw new ServiceException(HttpStatus.BAD_REQUEST, "SLA timeUnit cannot be null");
        }
        if (sla.getDurationValue() <= 0) {
            throw new ServiceException(HttpStatus.BAD_REQUEST, "SLA durationValue must be greater than 0");
        }
    }

    private void validateField(Object field, String fieldName) {
        if (field == null) {
            throw new ServiceException(HttpStatus.BAD_REQUEST, String.format("%s cannot be null", fieldName));
        }
    }
}
