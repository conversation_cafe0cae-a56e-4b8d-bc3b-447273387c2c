package com.wiom.partner.applications.dto.response;

import com.wiom.partner.applications.enums.TicketStatus;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketResponse {

    private String ticketId;
    private TicketStatus status;
    private String ticketUrl;
    private String ticketTag;

    public static TicketResponse of(PartnerCallLogs.Ticket ticket) {
        return TicketResponse.builder()
                .ticketId(ticket.getId())
                .status(ticket.getStatus())
                .ticketUrl(ticket.getUrl())
                .ticketTag(ticket.getTag())
                .build();
    }
}
