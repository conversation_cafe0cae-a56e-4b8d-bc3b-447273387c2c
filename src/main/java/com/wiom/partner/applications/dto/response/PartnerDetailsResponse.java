package com.wiom.partner.applications.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wiom.partner.applications.dto.callLogs.Disposition;
import com.wiom.partner.applications.dto.callLogs.Ticket;
import com.wiom.partner.applications.dto.request.TicketRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerDetailsResponse {

    private String partnerName;
    private String tag;  // logicalgroup
    private String callerRole; // e.g., admin, rohit, owner
    private String callerName;
    private Ticket ticket;
    private Disposition disposition;
}
