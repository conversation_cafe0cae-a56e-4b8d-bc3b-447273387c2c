package com.wiom.partner.applications.dto.response;

import com.wiom.partner.domain.entity.PartnerCallLogs;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerDetailsResponse {

    private String partnerName;
    private String tag;  // logicalgroup
    private String callerRole; // e.g., admin, rohit, owner
    private String callerName;
    private PartnerCallLogs.Ticket ticket;
    private PartnerCallLogs.Disposition disposition;
}
