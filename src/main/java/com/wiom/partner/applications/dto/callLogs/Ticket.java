package com.wiom.partner.applications.dto.callLogs;

import com.wiom.common.domain.dto.kapture.TicketIntegrationRequest;
import com.wiom.common.domain.dto.kapture.TicketIntegrationResponse;
import com.wiom.partner.applications.dto.request.TicketRequest;
import com.wiom.partner.applications.enums.TicketStatus;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Ticket implements Serializable {

    private String id;
    @Enumerated(EnumType.STRING)
    private TicketStatus status;
    private String url;
    private String tag;
    private LocalDateTime createdAt;
    // wiom request
    private TicketRequest request;
    // kapture request
    private TicketIntegrationRequest integrationRequest;

    public static Ticket of(TicketRequest ticketRequest, TicketIntegrationResponse kaptureResponse,
                              TicketIntegrationRequest integrationRequest, String url) {
        Ticket.TicketBuilder builder = Ticket.builder()
                .status(kaptureResponse == null ? TicketStatus.FAILED : TicketStatus.CREATED)
                .createdAt(LocalDateTime.now())
                .integrationRequest(integrationRequest)
                .request(ticketRequest);

        if (kaptureResponse != null) {
            builder.id(kaptureResponse.getTicketId())
                    .tag(kaptureResponse.getTicket().getTaskId())
                    .url(String.format(url, kaptureResponse.getTicket().getTaskId(), kaptureResponse.getTicketId()));
        }

        return builder.build();
    }
}
