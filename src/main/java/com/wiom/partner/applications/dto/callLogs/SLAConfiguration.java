package com.wiom.partner.applications.dto.callLogs;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SLAConfiguration implements Serializable {
    @Enumerated(EnumType.STRING)
    private TimeUnit timeUnit;  // e.g., HOURS, DAYS
    private long durationValue;  // e.g., 24 (hours/days)
    private long durationInMinutes;  // converted value
}
