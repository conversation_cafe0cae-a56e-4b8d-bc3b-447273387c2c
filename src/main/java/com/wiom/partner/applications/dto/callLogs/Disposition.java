package com.wiom.partner.applications.dto.callLogs;

import com.wiom.partner.applications.enums.Priority;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Disposition {

    private String category;
    private String subtype;
    private String bucket;
    private String qrc;
    private String department;
    @Enumerated(EnumType.STRING)
    private Priority priority;
    private SLAConfiguration sla;
    private String remark;
    private String deviceId;
    private String customerMobileNumber;
    private String question;
    private String status;
}
