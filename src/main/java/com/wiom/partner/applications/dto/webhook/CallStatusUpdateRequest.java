package com.wiom.partner.applications.dto.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;

@Data
@Slf4j
public class CallStatusUpdateRequest {
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("call_to_number")
    private String callToNumber;
    
    @JsonProperty("caller_id_number")
    private String callerIdNumber;
    
    @JsonProperty("call_id")
    private String callId;
    
    @JsonProperty("start_stamp")
    private OffsetDateTime startStamp;
    
    @JsonProperty("answer_stamp")
    private OffsetDateTime answerStamp;
    
    @JsonProperty("end_stamp")
    private OffsetDateTime endStamp;
    
    @JsonProperty("hangup_cause")
    private String hangupCause;
    
    @JsonProperty("billsec")
    private Long billSec;
    
    @JsonProperty("digits_dialed")
    private String digitsDialed;
    
    @JsonProperty("direction")
    private String direction;
    
    @JsonProperty("duration")
    private Long duration;
    
    @JsonProperty("answered_agent")
    private String answeredAgent;
    
    @JsonProperty("answered_agent_name")
    private String agentName;
    
    @JsonProperty("answered_agent_number")
    private String answeredAgentNumber;
    
    @JsonProperty("missed_agent")
    private String missedAgent;
    
    @JsonProperty("call_flow")
    private String callFlow;
    
    @JsonProperty("broadcast_lead_fields")
    private String broadcastLeadFields;
    
    @JsonProperty("recording_url")
    private String recordingUrl;
    
    @JsonProperty("recording_name")
    private String recordingName;
    
    @JsonProperty("call_status")
    private String callStatus;
    
    @JsonProperty("outbound_sec")
    private Long outboundSec;
    
    @JsonProperty("agent_ring_time")
    private Long agentRingTime;
    
    @JsonProperty("agent_transfer_ring_time")
    private Long agentTransferRingTime;
    
    @JsonProperty("billing_circle")
    private String billingCircle;
    
    @JsonProperty("call_connected")
    private Boolean callConnected;
    
    @JsonProperty("aws_call_recording_identifier")
    private String awsCallRecordingIdentifier;
    
    @JsonProperty("customer_no_with_prefix")
    private String customerNumberWithPrefix;
    
    @JsonProperty("campaign_name")
    private String campaignName;
    
    @JsonProperty("campaign_id")
    private String campaignId;
    
    @JsonProperty("customer_ring_time")
    private Long customerRingTime;
    
    @JsonProperty("reason_key")
    private String reasonKey;

}
