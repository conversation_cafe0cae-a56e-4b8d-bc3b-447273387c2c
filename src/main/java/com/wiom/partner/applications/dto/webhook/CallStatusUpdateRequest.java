package com.wiom.partner.applications.dto.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class CallStatusUpdateRequest {

    @JsonProperty("call_id")
    private String callId;
    
    @JsonProperty("hangup_cause")
    private String hangupCause;
    
    @JsonProperty("direction")
    private String direction;
    
    @JsonProperty("duration")
    private Long duration;
    
    @JsonProperty("answered_agent_name")
    private String agentName;
    
    @JsonProperty("recording_url")
    private String recordingUrl;
    
    @JsonProperty("call_status")
    private String callStatus;
    
}
