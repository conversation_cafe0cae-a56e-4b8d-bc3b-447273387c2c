package com.wiom.partner.applications.dto.callLogs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Caller {

    private String partnerName;
    private Long partnerId;
    private String zone;
    private String role; // (rohit/admin/owner)
    private String name; // caller name
    private String address;
}
