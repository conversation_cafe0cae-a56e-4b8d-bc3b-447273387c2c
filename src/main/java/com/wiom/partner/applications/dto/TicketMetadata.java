package com.wiom.partner.applications.dto;

import com.wiom.partner.applications.dto.callLogs.SLAConfiguration;
import com.wiom.partner.applications.enums.Priority;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketMetadata {

    private Long id;
    private String subtype; // L3
    private String qrc;
    private String department;
    private Priority priority;
    private SLAConfiguration sla;
    private String bucket; // L2
    private String category; // L1
}
