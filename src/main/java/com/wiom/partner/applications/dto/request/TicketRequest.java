package com.wiom.partner.applications.dto.request;

import com.wiom.partner.applications.enums.Priority;
import com.wiom.partner.domain.entity.TicketSubCategory;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketRequest implements Serializable {

    private String category; //
    private String subtype; //
    private String bucket; //
    private String department; //
    @Enumerated(EnumType.STRING)
    private Priority priority; //
    private TicketSubCategory.SLAConfiguration sla; //
    private String remark; //
    private String deviceId; //
    private String customerMobileNumber; //
    private String qrc; //
    private String question; //
}
