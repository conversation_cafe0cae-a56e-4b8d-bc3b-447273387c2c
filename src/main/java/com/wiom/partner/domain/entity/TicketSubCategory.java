package com.wiom.partner.domain.entity;

import com.vladmihalcea.hibernate.type.json.JsonType;
import com.wiom.common.domain.entity.BaseEntity;
import com.wiom.partner.applications.dto.callLogs.SLAConfiguration;
import com.wiom.partner.applications.enums.Priority;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.annotations.Type;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ticket_sub_category")
public class TicketSubCategory extends BaseEntity {

    @Column(name = "category_id")
    private Long categoryId;

    @Column(name = "subtype")
    private String subtype; // L3

    @Column(name = "qrc")
    private String qrc;

    @Column(name = "department")
    private String department;

    @Column(name = "priority")
    @Enumerated(EnumType.STRING)
    private Priority priority;

    @Column(name = "sla")
    @Type(JsonType.class)
    private SLAConfiguration sla;

    @Column(name = "bucket")
    private String bucket; // L2
}
