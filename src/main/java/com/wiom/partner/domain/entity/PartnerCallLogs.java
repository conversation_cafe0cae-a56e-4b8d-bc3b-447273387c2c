package com.wiom.partner.domain.entity;

import com.vladmihalcea.hibernate.type.json.JsonType;
import com.wiom.common.domain.entity.BaseEntity;
import com.wiom.partner.applications.dto.callLogs.Agent;
import com.wiom.partner.applications.dto.callLogs.Caller;
import com.wiom.partner.applications.dto.callLogs.Disposition;
import com.wiom.partner.applications.dto.callLogs.Ticket;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.applications.enums.IvrProvider;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "partner_call_log")
public class PartnerCallLogs extends BaseEntity {

    @Column(name = "call_id")
    private String callId;

    @Column(name = "ivr_provider")
    @Enumerated(EnumType.STRING)
    private IvrProvider ivrProvider;

    @Column(name = "from_number")
    private String fromNumber;

    @Column(name = "to_number")
    private String toNumber;

    @Column(name = "call_status")
    @Enumerated(EnumType.STRING)
    private CallStatus callStatus;

    @Type(JsonType.class)
    @Column(name = "caller_details")
    private Caller caller;

    @Type(JsonType.class)
    @Column(name = "disposition_details")
    private Disposition disposition;

    @Type(JsonType.class)
    @Column(name = "ticket_details")
    private Ticket ticket;

    @Type(JsonType.class)
    @Column(name = "agent")
    private Agent agent;
}
