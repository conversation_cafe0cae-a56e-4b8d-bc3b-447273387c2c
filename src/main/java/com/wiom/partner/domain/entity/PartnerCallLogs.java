package com.wiom.partner.domain.entity;

import com.vladmihalcea.hibernate.type.json.JsonType;
import com.wiom.common.domain.dto.kapture.TicketIntegrationRequest;
import com.wiom.common.domain.dto.kapture.TicketIntegrationResponse;
import com.wiom.common.domain.entity.BaseEntity;
import com.wiom.partner.applications.dto.request.TicketRequest;
import com.wiom.partner.applications.dto.webhook.CallStatusUpdateRequest;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.applications.enums.IvrProvider;
import com.wiom.partner.applications.enums.Priority;
import com.wiom.partner.applications.enums.TicketStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "partner_call_log")
public class PartnerCallLogs extends BaseEntity {

    @Column(name = "call_id")
    private String callId;

    @Column(name = "ivr_provider")
    @Enumerated(EnumType.STRING)
    private IvrProvider ivrProvider;

    @Column(name = "from_number")
    private String fromNumber;

    @Column(name = "to_number")
    private String toNumber;

    @Column(name = "call_status")
    @Enumerated(EnumType.STRING)
    private CallStatus callStatus;

    @Type(JsonType.class)
    @Column(name = "caller_details")
    private Caller caller;

    @Type(JsonType.class)
    @Column(name = "disposition_details")
    private Disposition disposition;

    @Type(JsonType.class)
    @Column(name = "ticket_details")
    private Ticket ticket;

    @Type(JsonType.class)
    @Column(name = "agent")
    private Agent agent;

    @Setter
    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Caller {

        private String partnerName;
        private Long partnerId;
        private String zone;
        private String role; // (rohit/admin/owner)
        private String name; // caller name
        private String address;
    }

    @Setter
    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Disposition {

        private String category;
        private String subtype;
        private String bucket;
        private String qrc;
        private String department;
        @Enumerated(EnumType.STRING)
        private Priority priority;
        private TicketSubCategory.SLAConfiguration sla;
        private String remark;
        private String deviceId;
        private String customerMobileNumber;
        private String question;
        private String status;
    }

    @Setter
    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Ticket implements Serializable {

        private String id;
        @Enumerated(EnumType.STRING)
        private TicketStatus status;
        private String url;
        private String tag;
        private LocalDateTime createdAt;
        // wiom request
        private TicketRequest request;
        // kapture request
        private TicketIntegrationRequest integrationRequest;

        public static Ticket of(TicketRequest ticketRequest, TicketIntegrationResponse kaptureResponse,
                                                                           TicketIntegrationRequest integrationRequest, String url) {
            Ticket.TicketBuilder builder = Ticket.builder()
                    .status(kaptureResponse == null ? TicketStatus.FAILED : TicketStatus.CREATED)
                    .createdAt(LocalDateTime.now())
                    .integrationRequest(integrationRequest)
                    .request(ticketRequest);

            if (kaptureResponse != null) {
                builder.id(kaptureResponse.getTicketId())
                        .tag(kaptureResponse.getTicket().getTaskId())
                        .url(String.format(url, kaptureResponse.getTicket().getTaskId(), kaptureResponse.getTicketId()));
            }

            return builder.build();
        }
    }

    @Setter
    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Agent {

        private String id;
        private String name;
        private Long duration;
        private String direction;
        private String recordingUrl;
        private String hangupCause;

        public static Agent of(CallStatusUpdateRequest request) {
            return Agent.builder()
                    .name(request.getAgentName())
                    .duration(request.getDuration())
                    .direction(request.getDirection())
                    .recordingUrl(request.getRecordingUrl())
                    .hangupCause(request.getHangupCause())
                    .build();
        }
    }
}
