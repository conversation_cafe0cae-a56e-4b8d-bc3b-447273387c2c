package com.wiom.partner.domain.specification;

import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import com.wiom.common.filters.FilterInfo;
import com.wiom.common.filters.FilterQuery;
import com.wiom.common.util.DateUtil;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import lombok.experimental.UtilityClass;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@UtilityClass
public class PartnerCallDetailsSpecification {

    public Specification<PartnerCallLogs> build(FilterInfo filterInfo, String fromNumber, String partnerName,
                                                CallStatus status) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Add standard filters
            addStandardFilters(predicates, criteriaBuilder, root, fromNumber, partnerName, status);

            // Add dynamic filters from FilterInfo
            addDynamicFilters(predicates, criteriaBuilder, root, filterInfo);

            // Log the number of predicates for debugging
            log.debug("Built PartnerCallDetails specification with {} predicates", predicates.size());

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private void addStandardFilters(List<Predicate> predicates,
                                    CriteriaBuilder criteriaBuilder,
                                    Root<PartnerCallLogs> root,
                                    String fromNumber, String partnerName,
                                    CallStatus status) {
        // Add exact match filters
        if (partnerName != null) {
            Expression<String> jsonPathExpr = criteriaBuilder.function(
                    "jsonb_extract_path_text",
                    String.class,
                    root.get("caller"),
                    criteriaBuilder.literal("partnerName")
            );
            predicates.add(criteriaBuilder.equal(jsonPathExpr, partnerName));
        }
        if (fromNumber != null) {
            predicates.add(criteriaBuilder.equal(root.get("fromNumber"), fromNumber));
        }
        if (status != null) {
            predicates.add(criteriaBuilder.equal(root.get("callStatus"), status));
        }
    }

    private void  addDynamicFilters(List<Predicate> predicates,
                                   CriteriaBuilder criteriaBuilder,
                                   Root<PartnerCallLogs> root,
                                   FilterInfo filterInfo) {
        if (filterInfo == null || filterInfo.getFilterQueryList() == null) {
            return;
        }

        for (FilterQuery<?> filterQuery : filterInfo.getFilterQueryList()) {
            String attribute = filterQuery.getAttribute();
            List<?> values = filterQuery.getValues();
            FilterQuery.Operations ops = filterQuery.getOps();

            if (attribute == null || values == null || values.isEmpty()) {
                continue;
            }

            log.debug("Processing dynamic filter for PartnerCallDetails: attribute={}, value={}, type={}, operation={}",
                    attribute, values, filterQuery.getType(), ops);

            switch (filterQuery.getType()) {
                case STRING -> addStringFilter(predicates, criteriaBuilder, root, attribute, values.get(0), ops);
                case NUMERIC -> addNumericFilter(predicates, criteriaBuilder, root, attribute, values.get(0), ops);
                case DATE -> {
                    if (ops == FilterQuery.Operations.BETWEEN) {
                        addDateFilter(predicates, criteriaBuilder, root, attribute, values, ops);
                    } else {
                        addDateFilter(predicates, criteriaBuilder, root, attribute, values.get(0), ops);
                    }
                }
                default -> log.debug("Unsupported filter type: {}", filterQuery.getType());
            }
        }
    }

    private void addStringFilter(List<Predicate> predicates,
                                 CriteriaBuilder criteriaBuilder,
                                 Root<PartnerCallLogs> root,
                                 String attribute, Object value, FilterQuery.Operations ops) {
        if (ops == FilterQuery.Operations.EQ) {
            predicates.add(criteriaBuilder.like(root.get(attribute), "%" + value + "%"));
        } else if (ops == FilterQuery.Operations.NOT_EQ) {
            predicates.add(criteriaBuilder.notLike(root.get(attribute), "%" + value + "%"));
        }
    }

    private void addNumericFilter(List<Predicate> predicates,
                                  CriteriaBuilder criteriaBuilder,
                                  Root<PartnerCallLogs> root,
                                  String attribute, Object value, FilterQuery.Operations ops) {
        if (value instanceof Number number) {
            if (ops == FilterQuery.Operations.EQ) {
                predicates.add(criteriaBuilder.equal(root.get(attribute), number));
            } else if (ops == FilterQuery.Operations.NOT_EQ) {
                predicates.add(criteriaBuilder.notEqual(root.get(attribute), number));
            }
        }
    }

    private void addDateFilter(List<Predicate> predicates,
                               CriteriaBuilder criteriaBuilder,
                               Root<PartnerCallLogs> root,
                               String attribute, Object value, FilterQuery.Operations ops) {
        // Handle date range filter (BETWEEN operation)
        if (ops == FilterQuery.Operations.BETWEEN && value instanceof List<?> dateValues && dateValues.size() >= 2) {
            addDateRangeFilter(predicates, criteriaBuilder, root, attribute, dateValues);
            return;
        }

        // Parse the date value
        LocalDateTime dateTime = null;
        if (value instanceof String str) {
            try {
                dateTime = LocalDate.parse(str).atStartOfDay();
            } catch (Exception e) {
                // Try using CoreUtil for more flexible date parsing
                LocalDate date = DateUtil.convertToDate(str);
                if (date != null) {
                    dateTime = date.atStartOfDay();
                }
            }
        } else if (value instanceof LocalDate localDate) {
            dateTime = localDate.atStartOfDay();
        } else if (value instanceof LocalDateTime dt) {
            dateTime = dt;
        }

        if (dateTime == null) {
            return;
        }

        // For equality operation, create a range for the entire day
        if (ops == FilterQuery.Operations.EQ) {
            LocalDateTime startOfDay = dateTime.toLocalDate().atStartOfDay();
            LocalDateTime endOfDay = dateTime.toLocalDate().atTime(23, 59, 59);
            predicates.add(criteriaBuilder.between(root.get(attribute), startOfDay, endOfDay));
        } else if (ops == FilterQuery.Operations.NOT_EQ) {
            LocalDateTime startOfDay = dateTime.toLocalDate().atStartOfDay();
            LocalDateTime endOfDay = dateTime.toLocalDate().atTime(23, 59, 59);
            predicates.add(criteriaBuilder.or(
                criteriaBuilder.lessThan(root.get(attribute), startOfDay),
                criteriaBuilder.greaterThan(root.get(attribute), endOfDay)
            ));
        }
    }

    private void addDateRangeFilter(List<Predicate> predicates,
                                    CriteriaBuilder criteriaBuilder,
                                    Root<PartnerCallLogs> root,
                                    String attribute, List<?> dateValues) {
        try {
            if (dateValues.size() < 2) {
                return;
            }
            LocalDateTime fromDate = parseDate(dateValues.get(0));
            LocalDateTime toDate = parseDate(dateValues.get(1));
            predicates.add(criteriaBuilder.between(root.get(attribute), fromDate, toDate));
        } catch (Exception e) {
            // Silently ignore invalid date values
        }
    }

    private LocalDateTime parseDate(Object obj) {
        try {
            return switch (obj) {
                case String str -> {
                    try {
                        yield LocalDate.parse(str).atStartOfDay();
                    } catch (Exception e) {
                        LocalDate date = DateUtil.convertToDate(str);
                        if (date != null) {
                            yield date.atStartOfDay();
                        }
                        throw new IllegalArgumentException("Cannot parse date string");
                    }
                }
                case LocalDate ld -> ld.atStartOfDay();
                case LocalDateTime ldt -> ldt;
                default -> throw new IllegalArgumentException("Unsupported date type");
            };
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to parse date", e);
        }
    }

}