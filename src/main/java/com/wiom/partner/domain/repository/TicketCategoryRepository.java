package com.wiom.partner.domain.repository;

import com.wiom.partner.domain.entity.TicketCategory;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TicketCategoryRepository extends JpaRepository<TicketCategory, Long> {

    List<TicketCategory> findAllByOrderById();

    Optional<TicketCategory> findByCategory(String category);
}
