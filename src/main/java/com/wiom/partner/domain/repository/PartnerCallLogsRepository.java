package com.wiom.partner.domain.repository;

import com.wiom.partner.domain.entity.PartnerCallLogs;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PartnerCallLogsRepository extends JpaRepository<PartnerCallLogs, Long>, JpaSpecificationExecutor<PartnerCallLogs> {

    Optional<PartnerCallLogs> findByCallId(String callId);

    Page<PartnerCallLogs> findAll(Pageable pageable);
}
