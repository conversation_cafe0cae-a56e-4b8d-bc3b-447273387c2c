package com.wiom.partner.domain.repository;

import com.wiom.partner.domain.entity.TicketSubCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TicketSubCategoryRepository extends JpaRepository<TicketSubCategory, Long> {

    List<TicketSubCategory> findAllByOrderById();

    Optional<TicketSubCategory> findBySubtype(String subtype);
}
