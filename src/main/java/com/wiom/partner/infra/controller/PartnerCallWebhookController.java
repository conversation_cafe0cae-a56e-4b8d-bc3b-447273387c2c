package com.wiom.partner.infra.controller;

import com.wiom.partner.applications.service.PartnerCallWebhookService;
import com.wiom.partner.applications.dto.webhook.CallStatusUpdateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/partner/v1/webhook")
@RequiredArgsConstructor
public class PartnerCallWebhookController {

    private final PartnerCallWebhookService partnerCallWebhookService;

    @PostMapping("/call-status")
    public ResponseEntity<String> updateCallStatus(@RequestBody Map<Object, Object> request) {
        log.info("Webhook Received Successfully: Processing CallStatusUpdateRequest: {}", request);
//        partnerCallWebhookService.updateCallStatus(request);
        return ResponseEntity.ok("Call status updated successfully");
    }
}
