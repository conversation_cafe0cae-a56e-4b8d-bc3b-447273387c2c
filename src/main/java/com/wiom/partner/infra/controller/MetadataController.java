package com.wiom.partner.infra.controller;

import com.wiom.partner.applications.service.TicketMetadataService;
import com.wiom.partner.applications.dto.response.TicketReferenceData;
import com.wiom.partner.applications.dto.request.TicketCategoryRequest;
import com.wiom.partner.applications.dto.TicketMetadata;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/partner/v1/metadata")
@RequiredArgsConstructor
public class MetadataController {

    private final TicketMetadataService dispositionMasterDataService;

    @GetMapping("/ticket")
    public ResponseEntity<TicketReferenceData> getTicketMetadata() {
        return ResponseEntity.ok(dispositionMasterDataService.getTicketMetadata());
    }

    @PostMapping("/ticket")
    public ResponseEntity<String> saveTicketMetadata(
            @RequestBody List<TicketMetadata> masterDataList) {
        dispositionMasterDataService.saveTicketMetadata(masterDataList);
        return ResponseEntity.ok("Data saved successfully");
    }

    @PutMapping("/ticket/category")
    public ResponseEntity<String> updateTicketCategory(
            @RequestBody TicketCategoryRequest masterDataList) {
        dispositionMasterDataService.updateTicketCategory(masterDataList);
        return ResponseEntity.ok("Data updated successfully");
    }
}
