package com.wiom.partner.infra.controller;

import com.wiom.partner.applications.dto.request.TicketRequest;
import com.wiom.partner.applications.dto.response.TicketResponse;
import com.wiom.partner.applications.service.TicketManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/partner/v1")
@RequiredArgsConstructor
public class TicketManagementController {

    private final TicketManagementService ticketManagementService;

    /**
     *Api for ticket creation on kapture for partner complaint.
     * @param callId
     * @param ticket
     *
     */
    @PutMapping("/{callId}/ticket")
    public ResponseEntity<TicketResponse> raiseTicket(@PathVariable String callId, @RequestBody TicketRequest ticket) {
        return ResponseEntity.ok(ticketManagementService.raiseTicket(callId, ticket));
    }
}
