package com.wiom.partner.infra.controller;

import com.wiom.partner.applications.dto.callLogs.Disposition;
import com.wiom.partner.applications.enums.CallStatus;
import com.wiom.partner.applications.service.PartnerIvrCallService;
import com.wiom.partner.applications.dto.tataTele.TataTelecomPartnerRequest;
import com.wiom.partner.applications.dto.response.PaginatedResponse;
import com.wiom.partner.applications.dto.tataTele.TataTelecom;
import com.wiom.partner.applications.dto.response.PartnerDetailsResponse;
import com.wiom.partner.domain.entity.PartnerCallLogs;
import com.wiom.common.filters.FilterInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/partner/v1")
@RequiredArgsConstructor
public class PartnerCallManagementController {

    private final PartnerIvrCallService partnerIvrCallService;

    /**
     * @param tataTelecomPartnerRequest
     *  Call Initiated
     */
    @PostMapping("/initiate")
    public List<TataTelecom> handleIncomingCall(@RequestBody TataTelecomPartnerRequest tataTelecomPartnerRequest) {
        List<TataTelecom> tataTelecom = partnerIvrCallService.handleIncomingCall(tataTelecomPartnerRequest);
        log.info("[handleIncomingCall] tataTelecom response : {} for number: {}", tataTelecom, tataTelecomPartnerRequest.getCallerIdNumber());
        return tataTelecom;
    }

    /**
     *
     * @param callId
     * Call Connected
     */
    @PutMapping("/connect")
    public PartnerDetailsResponse initializePartnerCallSession(@RequestParam("callId") String callId) {
        return partnerIvrCallService.initializePartnerCallSession(callId);
    }

    /**
     *
     * @param callId
     * @param disposition
     * status
     */
    @PutMapping("/{callId}/disposition")
    public ResponseEntity<String> saveDisposition(@PathVariable String callId, @RequestBody Disposition disposition) {
        partnerIvrCallService.saveDisposition(callId, disposition);
        return ResponseEntity.ok("Disposition saved");
    }

    /**
     * Fetch call logs for all the partners
     * @param pageNumber
     * @param pageSize
     * @param partnerName
     * @param fromNumber
     * @param status
     * @param query
     * @return
     */
    @GetMapping("/call-logs")
    public PaginatedResponse<PartnerCallLogs> fetchCallLogs(
            @RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String fromNumber,
            @RequestParam(required = false) String partnerName,
            @RequestBody(required = false) CallStatus status, FilterInfo query) {
        return partnerIvrCallService.fetchCallLogs(pageNumber, pageSize, fromNumber, partnerName, status, query);
    }
}
