package com.wiom.ticketing.dto.request;

import com.wiom.ticketing.entity.Ticket.TicketCategory;
import com.wiom.ticketing.entity.Ticket.TicketPriority;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateTicketRequest {

    @NotBlank(message = "Title is required")
    private String title;

    @NotBlank(message = "Description is required")
    private String description;

    @NotNull(message = "Priority is required")
    private TicketPriority priority;

    @NotNull(message = "Category is required")
    private TicketCategory category;

    @NotBlank(message = "Reporter ID is required")
    private String reporterId;

    @NotBlank(message = "Reporter name is required")
    private String reporterName;

    private String reporterEmail;

    private String assigneeId;

    private String assigneeName;

    private LocalDateTime dueDate;

    private Integer estimatedHours;

    private List<String> tags;
}
