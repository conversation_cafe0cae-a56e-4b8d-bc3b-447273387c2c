# IVR Service
IVR Service is a Spring Boot application that provides APIs for managing IVR calls.

## Features
- Handle incoming calls from Tata Telecom
- Initialize partner call session
- Save call dispositions
- Fetch call logs

## Architecture Diagram


## Requirements

- Java 21
- PostgreSQL
- Docker (for containerized deployment)


## Resource Requirements

### AWS ECS/EKS:

CPU: 2 vCPU
Memory: 4GB


## Deployment with Docker

### Manual Build and Run

If you prefer to build and run the Docker image manually:

1. Build the Docker image:
   ```bash
   docker build -t ivr-service:latest .
   ```

2. Run the Docker container:
   ```bash
   docker run -d -p 8080:8080 \
     -e SPRING_DATASOURCE_URL=*************************************** \
     -e SPRING_DATASOURCE_USERNAME=abc \
     -e SPRING_DATASOURCE_PASSWORD=xxx \
     --name ivr-service ivr-service:latest
   ```