apiVersion: v1
kind: Namespace
metadata:
  name: ivr-v2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ivr-v2-qa-deployment
  namespace : ivr-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ivr-v2-qa
  template:
    metadata:
      labels:
        app: ivr-v2-qa
    spec:
      containers:
      - name: ivr-v2-qa
        image: 099219084111.dkr.ecr.ap-south-2.amazonaws.com/ivr-v2:latest
        ports:
        - containerPort: 8080  
        env:
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: DD_TRACE_AGENT_PORT
          value: "8126"
        - name: DD_ENV
          value: "qa" 
        - name: DD_SERVICE
          value: "ivr-v2-qa"
        - name: DD_VERSION
          value: "1.0.0"
        - name: DD_TRACE_ENABLED
          value: "true"
        - name: CORECLR_ENABLE_PROFILING
          value: "1"
        - name: CORECLR_PROFILER
          value: "{846F5F1C-F9AE-4B07-969E-05C26BC060D8}"
        - name: CORECLR_PROFILER_PATH
          value: "/opt/datadog/Datadog.Trace.ClrProfiler.Native.so"
        - name: DD_DOTNET_TRACER_HOME
          value: "/opt/datadog"
        
---
apiVersion: v1
kind: Service
metadata:
  namespace: ivr-v2
  name: ivr-v2-qa-service
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "12m"
    nginx.ingress.kubernetes.io/proxy-buffers: "8 2m"
spec:
  type: ClusterIP
  selector:
    app: ivr-v2-qa
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
    
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ivr-v2-qa-ingress
  namespace: ivr-v2
spec:
  ingressClassName: nginx
  rules:
  - host: ivr-v2.qa.i2e1.in
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ivr-v2-qa-service
            port:
              number: 80