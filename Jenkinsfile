pipeline {
    agent any

    environment {
        ECR_REGISTRY = "099219084111.dkr.ecr.${params.AWS_REGION}.amazonaws.com"
        ECR_REPOSITORY = 'ivr-v2'
        IMAGE_TAG = 'latest'
    }

    stages {
        stage('Select Environment') {
            steps {
                script {
                    env.SELECTED_ENV = params.ENVIRONMENT
                    echo "Selected Environment: ${env.SELECTED_ENV}"
                }
            }
        }

        stage('Checkout') {
            steps {
                checkout scm
            }
        }

        // stage('Build Docker Image Dev') {
        //     when {
        //         expression { env.SELECTED_ENV == 'dev' }
        //     }
        //     steps {
        //         script {
        //             def imageTag = "${env.BUILD_NUMBER}"
        //             def imageName = "${ECR_REPO_DEV}:${imageTag}"
                    
        //             sh "aws ecr get-login-password --region ${params.AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPO_DEV}"
        //             sh "docker build -t ${imageName} -f customer.Dockerfile  --build-arg ENVIRONMENT=${params.ENVIRONMENT} --build-arg SERVER=customer-${params.ENVIRONMENT}-i2e1 ."
        //             sh "docker tag ${imageName} ${ECR_REPO_DEV}:latest"
        //             sh "docker push ${ECR_REPO_DEV}:${imageTag}"
        //             sh "docker push ${ECR_REPO_DEV}:latest"
                    
        //             env.IMAGE_TAG = imageTag
        //             }
        //         }
        //     }

        // stage('Update Task Definition Dev') {
        //     when {
        //         expression { env.SELECTED_ENV == 'dev' }
        //     }
        //     steps {
        //         script {
        //             sh "jq '.containerDefinitions[0].image = \"${ECR_REPO_DEV}:${IMAGE_TAG}\"' ${TASK_DEF_FILE_DEV} > new_task_def.json"
        //             sh "aws ecs register-task-definition --cli-input-json file://new_task_def.json > task_def_output.json"
                    
        //             def taskDefArn = sh(script: "jq -r '.taskDefinition.taskDefinitionArn' task_def_output.json", returnStdout: true).trim()
        //             env.TASK_DEF_ARN = taskDefArn
        //         }
        //     }
        // }

        // stage('Update ECS Service DEV') {
        //     when {
        //         expression { env.SELECTED_ENV == 'dev' }
        //     }
        //     steps {
        //         script {
        //             sh "aws ecs update-service --cluster ${ECS_CLUSTER_DEV} --service ${ECS_SERVICE_DEV} --task-definition ${TASK_DEF_ARN}"
        //         }
        //     }
        // }
        
        stage('Build Docker Image QA') {
            when {
                expression { env.SELECTED_ENV == 'qa' }
            }
            steps {
                    // Specify the path to the Dockerfile using -f
                    script {
                    sh 'ls -la' // This will list the contents of I2E1Web
                    sh "docker build -t ${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG} -f Dockerfile  --build-arg ENVIRONMENT=${params.ENVIRONMENT} ."
                    }
                }
            }
            
        stage('Push Docker Image to ECR QA') {
            when {
                expression { env.SELECTED_ENV == 'qa' }
            }
            steps {
                script {
                    sh """
                    aws ecr get-login-password --region ${params.AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REGISTRY}
                    docker push ${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}
                    """
                }
            }
        }
        stage('Deploy to EKS QA') {
            when {
                expression { env.SELECTED_ENV == 'qa' }
            }
            steps {
                script {
					sh "aws eks --region ${params.AWS_REGION} update-kubeconfig --name ${params.ENVIRONMENT}-eks-cluster"
                    sh "kubectl apply -f deployment.${params.ENVIRONMENT}.yaml"
                    if (env.environment == 'dev' || env.environment == 'qa' ) {
					    sh "kubectl rollout restart deployment ${ECR_REPOSITORY}-${params.ENVIRONMENT}-deployment -n ${ECR_REPOSITORY}"
                    } else {
                        sh "kubectl rollout restart deployment ${ECR_REPOSITORY}-${params.ENVIRONMENT}-deployment"
                    }
                }
            }
        }

    }
}
