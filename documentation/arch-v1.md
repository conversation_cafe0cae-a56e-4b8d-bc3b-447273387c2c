```mermaid
C4Context

    Enterprise_Boundary(wiom, "Wiom") {

        System(userRegistry, "User Registory Service", "Manages IVR calls.")



        Boundary(ivrb, "IVR") {
            System(ivr, "IVR Service", "Manages IVR calls.")
            SystemDb(ivrdb, "IVR Database", "Store IVR call logs.")

        }

        Boundary(ivrb, "IVR") {
            System(ivr, "IVR Service", "Manages IVR calls.")
            SystemDb(ivrdb, "IVR Database", "Store IVR call logs.")

        }


    }

    Boundary(tts, "Tata TeleService") {
        System(tele, "IVR Provider", "IVR provider.")
    }

    Boundary(kap, "Kapture") {
        System(ticket, "Ticket  Service", "IVR provider.")
    }

    Rel(ivr, ticket, "Get and create ticket")

    Rel(ivr, userRegistry, "Partner and customer detail")

    Rel(tele, ivr, "connect ivr calls, callbacks.")
    Rel(ivr, ivrdb, "store/retrieve call logs")
    UpdateRelStyle(ivrdb, ivr, "red", "blue", "-40", "60")
    UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="2")



```